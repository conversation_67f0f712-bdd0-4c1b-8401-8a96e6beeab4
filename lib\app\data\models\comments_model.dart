import 'package:ibcb_desktop/app/data/models/user_model.dart';

class CommentsModel {
  int id;
  int postagem_id;
  int user_id;
  // String autor_nome;
  // String autor_imagem;
  String conteudo;
  DateTime criado;
  UserModel user;

  CommentsModel({
    required this.id,
    required this.postagem_id,
    required this.user_id,
    // required this.autor_nome,
    // required this.autor_imagem,
    required this.conteudo,
    required this.criado,
    required this.user,
  });

  // Map<String, dynamic> toMap() {
  //   return {
  //     'id': id,
  //     'postagem_id': postagem_id,
  //     'user_id': user_id,
  //     'autor_nome': autor_nome,
  //     'autor_imagem': autor_imagem,
  //     'conteudo': conteudo,
  //     'criado': criado.millisecondsSinceEpoch,
  //   };
  // }

  factory CommentsModel.fromMap(Map<String, dynamic> map) {
    return CommentsModel(
      id: map['id']?.toInt() ?? 0,
      postagem_id: map['postagem_id']?.toInt() ?? 0,
      user_id: map['user_id']?.toInt() ?? 0,
      // autor_nome: map['autor_nome'] ?? '',
      // autor_imagem: map['autor_imagem'] ?? '',
      conteudo: map['conteudo'] ?? '',
      criado: DateTime.parse(map['criado']),
      user: UserModel.fromMap(map['user']),
    );
  }
}
