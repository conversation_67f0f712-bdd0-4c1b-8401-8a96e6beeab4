import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:ibcb_desktop/app/helpers/snack_bar_msg.dart';
import 'package:image_picker/image_picker.dart';

class SelectCompressImage {
  Rx<File?> image = Rx<File?>(null);
  final picker = ImagePicker();
  final Rx<Uint8List?> compressedImage = Rx<Uint8List?>(null);

  Future<void> selectAndCompressImage() async {
    try {
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        final file = File(pickedFile.path);
        final result = await FlutterImageCompress.compressWithFile(
          file.path,
          quality: 25,
        );
        image.value = file;
        compressedImage.value = result;
        SnackBarMsg().showSnack(
          'Imagem selecionada...',
          'Imagen enviada para Compresão',
        );
      }
    } catch (e) {
      SnackBarMsg().showSnack(
        'Erro!!!',
        'Erro ao selecionar/comprimir imagem: $e',
      );
    }
  }
}

class SelectCompressImages {
  List<Uint8List> compressedImages = [];
  final Rx<Uint8List?> compressedImage = Rx<Uint8List?>(null);
  List<File> imagesList = [];

  Future<List<File>> pickImages() async {
    final picker = ImagePicker();

    try {
      List<XFile> pickedFiles = await picker.pickMultiImage(imageQuality: 25);

      for (var pickedFile in pickedFiles) {
        final imageFile = File(pickedFile.path);
        imagesList.add(imageFile);
      }
      SnackBarMsg().showSnack(
        'Imagens selecionadas...',
        'Imagens enviadas para Compresão',
      );
      return imagesList;
    } catch (e) {
      SnackBarMsg().showSnack('Erro!!!', 'Erro ao selecionar imagens');
      throw '$e';
    }
  }

  Future<List<Uint8List>> compressImages() async {
    try {
      await pickImages();
      for (var image in imagesList) {
        var compressedImage = await FlutterImageCompress.compressWithFile(
          image.path,
          quality: 30,
        );
        compressedImages.add(compressedImage!);
      }

      SnackBarMsg().showSnack(
        'Imagens Comprimidas...',
        'Enviando Imagens para o Servidor',
      );
      return compressedImages;
    } catch (e) {
      SnackBarMsg().showSnack('Erro!!!', 'Erro ao Comprimir imagens');
      throw '$e';
    }
  }
}
