// import 'package:dio/dio.dart';
// import 'package:flutter/material.dart';
// import 'package:get/instance_manager.dart';
// import 'package:ibcb_desktop/app/core/logger/app_logger.dart';
// import 'package:ibcb_desktop/app/data/models/evento_model.dart';
// import 'package:ibcb_desktop/app/data/models/forum_model.dart';
// import 'package:ibcb_desktop/app/data/models/posts_model.dart';
// import 'package:ibcb_desktop/app/data/models/role.dart';
// import 'package:ibcb_desktop/app/data/models/user_login_req_model.dart';
// import 'package:ibcb_desktop/app/data/models/user_login_res_model.dart';
// import 'package:ibcb_desktop/app/data/models/user_model.dart';
// import 'package:ibcb_desktop/app/data/models/user_profile_model.dart';
// import 'package:ibcb_desktop/app/data/providers/api_exception.dart';
// import 'package:ibcb_desktop/app/data/services/storage/storage_service.dart';

// class Api {
//   final Dio _dio;
//   final StorageService _storageService = Get.find<StorageService>();
//   static final _logger = ModuleLogger.create('API');
//   Api()
//     : _dio = Dio(
//         BaseOptions(
//           baseUrl: 'http://192.168.0.40:3333/',
//           // baseUrl: 'https://realdev.top/',
//           connectTimeout: const Duration(seconds: 60),
//           receiveTimeout: const Duration(seconds: 120),
//           sendTimeout: const Duration(seconds: 60),
//           headers: {
//             'Accept': 'application/json',
//             'Content-Type': 'application/json',
//           },
//         ),
//       ) {
//     _dio.interceptors.add(
//       InterceptorsWrapper(
//         onRequest: (options, handler) async {
//           String? token = _storageService.token;
//           if (token != null) {
//             options.headers['Authorization'] = 'Bearer $token';
//           } // Handle different data types for logging
//           Map<String, dynamic>? logData;
//           if (options.data is Map<String, dynamic>) {
//             logData = options.data as Map<String, dynamic>;
//           } else if (options.data is FormData) {
//             // For FormData, create a summary map for logging
//             final formData = options.data as FormData;
//             logData = {
//               'fields': Map.fromEntries(formData.fields),
//               'files':
//                   formData.files
//                       .map(
//                         (file) => {
//                           'key': file.key,
//                           'value':
//                               'MultipartFile(${file.value.filename ?? 'unknown'})',
//                         },
//                       )
//                       .toList(),
//             };
//           }
//           _logger.apiRequest(options.method, options.path, logData);
//           return handler.next(options);
//         },
//         onResponse: (response, handler) async {
//           _logger.apiResponse(
//             response.requestOptions.method,
//             response.requestOptions.path,
//             response.statusCode ?? 0,
//             response.data,
//           );
//           return handler.next(response);
//         },
//         onError: (DioException e, handler) async {
//           _logger.error('API Error: $e', e);
//           if (e.response != null) {
//             _logger.error('API Error Response Data: \\n${e.response?.data}', e);
//           }
//           // Tratar erros de timeout especificamente
//           if (e.type == DioExceptionType.connectionTimeout) {
//             handler.reject(
//               DioException(
//                 requestOptions: e.requestOptions,
//                 type: DioExceptionType.connectionTimeout,
//                 error: NetworkException(
//                   'Tempo limite de conexão excedido. Verifique sua conexão com a internet e tente novamente.',
//                 ),
//               ),
//             );
//             return;
//           }

//           if (e.type == DioExceptionType.receiveTimeout) {
//             handler.reject(
//               DioException(
//                 requestOptions: e.requestOptions,
//                 type: DioExceptionType.receiveTimeout,
//                 error: NetworkException(
//                   'Tempo limite de recepção excedido. O servidor está demorando muito para responder.',
//                 ),
//               ),
//             );
//             return;
//           }

//           if (e.type == DioExceptionType.sendTimeout) {
//             handler.reject(
//               DioException(
//                 requestOptions: e.requestOptions,
//                 type: DioExceptionType.sendTimeout,
//                 error: NetworkException(
//                   'Tempo limite de envio excedido. Verifique sua conexão com a internet.',
//                 ),
//               ),
//             );
//             return;
//           }

//           if (e.type == DioExceptionType.connectionError) {
//             handler.reject(
//               DioException(
//                 requestOptions: e.requestOptions,
//                 type: DioExceptionType.connectionError,
//                 error: NetworkException(
//                   'Erro de conexão. Verifique se você está conectado à internet e se o servidor está disponível.',
//                 ),
//               ),
//             );
//             return;
//           }

//           if (e.type == DioExceptionType.unknown) {
//             handler.reject(
//               DioException(
//                 requestOptions: e.requestOptions,
//                 type: DioExceptionType.unknown,
//                 error: NetworkException(
//                   'Erro de rede desconhecido. Verifique sua conexão com a internet.',
//                 ),
//               ),
//             );
//             return;
//           }

//           if (e.response != null) {
//             String? backendMessage;
//             if (e.response?.data is Map &&
//                 e.response?.data['message'] != null) {
//               backendMessage = e.response?.data['message'].toString();
//             } else if (e.response?.data is String) {
//               backendMessage = e.response?.data;
//             }
//             switch (e.response!.statusCode) {
//               case 400:
//                 handler.reject(
//                   DioException(
//                     requestOptions: e.requestOptions,
//                     response: e.response,
//                     type: DioExceptionType.badResponse,
//                     error: BadRequestException(
//                       backendMessage ??
//                           'Requisição inválida: ${e.response!.data}',
//                     ),
//                   ),
//                 );
//                 break;
//               case 401:
//                 if (e.response!.data['message'] == 'Invalid credentials') {
//                   handler.reject(
//                     DioException(
//                       requestOptions: e.requestOptions,
//                       response: e.response,
//                       type: DioExceptionType.badResponse,
//                       error: InvalidCredentialsException(
//                         backendMessage ??
//                             'Credenciais inválidas: ${e.response!.data}',
//                       ),
//                     ),
//                   );
//                 } else {
//                   handler.reject(
//                     DioException(
//                       requestOptions: e.requestOptions,
//                       response: e.response,
//                       type: DioExceptionType.badResponse,
//                       error: UnauthorizedException(
//                         backendMessage ?? 'Não autorizado: ${e.response!.data}',
//                       ),
//                     ),
//                   );
//                 }
//                 break;
//               case 404:
//                 handler.reject(
//                   DioException(
//                     requestOptions: e.requestOptions,
//                     response: e.response,
//                     type: DioExceptionType.badResponse,
//                     error: NotFoundException(
//                       backendMessage ?? 'Não encontrado: ${e.response!.data}',
//                     ),
//                   ),
//                 );
//                 break;
//               case 500:
//                 handler.reject(
//                   DioException(
//                     requestOptions: e.requestOptions,
//                     response: e.response,
//                     type: DioExceptionType.badResponse,
//                     error: InternalServerException(
//                       backendMessage ??
//                           'Erro interno do servidor: ${e.response!.data}',
//                     ),
//                   ),
//                 );
//                 break;
//               default:
//                 handler.reject(
//                   DioException(
//                     requestOptions: e.requestOptions,
//                     response: e.response,
//                     type: DioExceptionType.badResponse,
//                     error: UnknownException(
//                       backendMessage ??
//                           'Erro desconhecido: ${e.response!.data}',
//                     ),
//                   ),
//                 );
//             }
//           } else {
//             handler.reject(
//               DioException(
//                 requestOptions: e.requestOptions,
//                 type: DioExceptionType.badResponse,
//                 error: NetworkException('Erro de rede: ${e.message}'),
//               ),
//             );
//           }
//           // Removendo a chamada handler.next(e) para evitar que o handler seja chamado mais de uma vez
//         },
//       ),
//     );
//   }
//   Future<UserModel> getUser() async {
//     return _executeWithRetry(() async {
//       try {
//         Response response = await _dio.get('auth');
//         AppLogger.logger.debug('getUser response data: ${response.data}');
//         return UserModel.fromMap(response.data['user']);
//       } on DioException catch (e) {
//         throw _handleDioException(e);
//       }
//     });
//   }

//   Future<UserLoginResponseModel> login(UserLoginReqModel data) async {
//     return _executeWithRetry(() async {
//       try {
//         Response response = await _dio.post('login', data: data.toMap());
//         AppLogger.logger.debug('login response data: ${response.data}');
//         return UserLoginResponseModel.fromMap(response.data);
//       } on DioException catch (e) {
//         throw _handleDioException(e);
//       }
//     });
//   }

//   Future<Map<String, dynamic>> authenticateWithGoogle(String idToken) async {
//     try {
//       Response response = await _dio.post(
//         'auth/google',
//         data: {'idToken': idToken},
//       );
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   Future<void> sendFcmTokenToServer(String fcmToken, String accessToken) async {
//     try {
//       await _dio.post(
//         'save-fcm-token',
//         data: {'fcmToken': fcmToken},
//         options: Options(headers: {'Authorization': 'Bearer $accessToken'}),
//       );
//       debugPrint('FCM token saved successfully');
//     } catch (e) {
//       debugPrint('Failed to save FCM token');
//     }
//   }

//   Future<void> register(UserLoginReqModel data) async {
//     try {
//       Response response = await _dio.post('register', data: data.toMap());
//       debugPrint('register response data: ${response.data}');
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   Future<void> logout() async {
//     try {
//       Response response = await _dio.get('logout');
//       debugPrint('logout response data: ${response.data}');
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   Future<void> notifyApiPasswordChanged(
//     String email,
//     String newPassword,
//     String? firebaseToken,
//   ) async {
//     try {
//       Response response = await _dio.post(
//         'password-reset/notify',
//         data: {
//           'email': email,
//           'new_password': newPassword,
//           'firebase_token': firebaseToken,
//         },
//       );
//       debugPrint('API notified about password change: ${response.data}');
//     } on DioException catch (e) {
//       debugPrint('Failed to notify API about password change: $e');
//     }
//   }

//   //----------------------------------------------------------------
//   Future<List<UserModel>> fetchUsers() async {
//     try {
//       Response response = await _dio.get('profile');
//       AppLogger.logger.debug('fetchUsers response data: ${response.data}');

//       if (response.data is List) {
//         List<UserModel> users =
//             (response.data as List)
//                 .map(
//                   (user) =>
//                       user is Map<String, dynamic>
//                           ? UserModel.fromMap(user)
//                           : null,
//                 )
//                 .where((user) => user != null)
//                 .cast<UserModel>()
//                 .toList();
//         return users;
//       } else {
//         throw Exception('Resposta da API não é uma lista');
//       }
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     } catch (e) {
//       throw Exception('Erro ao processar usuários: $e');
//     }
//   }

//   Future<UserProfileModel> updateProfile(UserProfileModel data) async {
//     debugPrint("Api: updateProfile called");
//     try {
//       FormData formData = FormData.fromMap({'nome': data.nome});

//       if (data.imagemPerfil != null) {
//         // Check if it's a local path or a URL
//         if (data.imagemPerfil!.startsWith('http')) {
//           // If it's a URL, no need to send the file data
//           formData.fields.add(MapEntry('imagem_perfil', data.imagemPerfil!));
//         } else {
//           // If it's a path, send the file as a multipart
//           formData.files.add(
//             MapEntry(
//               'imagem_perfil',
//               await MultipartFile.fromFile(data.imagemPerfil!),
//             ),
//           );
//         }
//       }

//       Response response = await _dio.put('profile', data: formData);
//       return UserProfileModel.fromMap(response.data);
//     } on DioException catch (e) {
//       // ... (error handling)
//       throw UnknownException('Erro ao atualizar perfil: ${e.message}');
//     }
//   }

//   //----------------------------------------------------------------
//   Future<List<EventoModel>> getEvent() async {
//     try {
//       Response response = await _dio.get('event');
//       List<dynamic> data = response.data as List<dynamic>;
//       return data.map((item) => EventoModel.fromMap(item)).toList();
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   Future<void> createEvent(FormData data) async {
//     try {
//       Response response = await _dio.post('event', data: data);
//       debugPrint('postEvent response data: ${response.data}');
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   Future<void> deleteEvent(int eventId) async {
//     try {
//       Response response = await _dio.delete('event/$eventId');
//       debugPrint('deleteEvent response data: ${response.data}');
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   //----------------------------------------------------------------
//   Future<Map<String, dynamic>> getPosts({int page = 1, int perPage = 5}) async {
//     final response = await _dio.get(
//       'post',
//       queryParameters: {'page': page, 'perPage': perPage},
//     );
//     return response.data;
//   }

//   Future<void> createPost(FormData data) async {
//     try {
//       Response response = await _dio.post('post', data: data);
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   Future<PostsModel> updatePost(int id, FormData data) async {
//     try {
//       Response response = await _dio.put('post/$id', data: data);
//       return PostsModel.fromMap(response.data);
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   Future<void> deletePost(int id) async {
//     try {
//       Response response = await _dio.delete('post/$id');
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   Future<Map<String, dynamic>> createCommentPost(
//     int forumId,
//     String content,
//   ) async {
//     try {
//       Response response = await _dio.post(
//         '/post/$forumId/comment',
//         data: {'conteudo': content},
//       );
//       return response.data; // Retorna os dados do comentário criado
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   deleteCommentsPost(int id) async {
//     try {
//       Response response = await _dio.delete('post/$id/comment');
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   //----------------------------------------------------------------
//   Future<List<Role>> fetchCargos() async {
//     try {
//       final response = await _dio.get('/cargos');
//       debugPrint('fetchCargos response: ${response.data}');

//       if (response.data is List) {
//         return (response.data as List)
//             .map((e) => e is Map<String, dynamic> ? Role.fromMap(e) : null)
//             .where((role) => role != null)
//             .cast<Role>()
//             .toList();
//       } else {
//         throw Exception('Resposta da API não é uma lista');
//       }
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     } catch (e) {
//       throw Exception('Erro ao processar cargos: $e');
//     }
//   }

//   Future<void> updateUserCargos(int userId, List<int> cargoIds) async {
//     try {
//       debugPrint('Atualizando cargos do usuário $userId: $cargoIds');
//       final response = await _dio.post(
//         '/usuarios/$userId/cargos',
//         data: {'cargos': cargoIds},
//       );
//       debugPrint('Resposta updateUserCargos: ${response.data}');
//     } on DioException catch (e) {
//       debugPrint('Erro DioException em updateUserCargos: ${e.message}');
//       throw _handleDioException(e);
//     } catch (e) {
//       debugPrint('Erro geral em updateUserCargos: $e');
//       throw Exception('Erro ao atualizar cargos do usuário: $e');
//     }
//   }

//   //----------------------------------------------------------------

//   Future<Map<String, dynamic>> fetchForumTopics({
//     int page = 1,
//     int perPage = 5,
//   }) async {
//     try {
//       Response response = await _dio.get(
//         'forum',
//         queryParameters: {'page': page, 'perPage': perPage},
//       );
//       return response.data;
//     } on DioException catch (e) {
//       debugPrint(e.message);
//       throw _handleDioException(e);
//     }
//   }

//   Future<void> createForumTopic(FormData data) async {
//     try {
//       Response response = await _dio.post('forum', data: data);
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   Future<void> updateForumTopic(int id, Map<String, dynamic> data) async {
//     try {
//       await _dio.put('forum/$id', data: data);
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   Future<void> deleteForumTopic(int id) async {
//     try {
//       await _dio.delete('forum/$id');
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   Future<ForumModel> fetchForumTopicById(int topicId) async {
//     try {
//       Response response = await _dio.get('forum/$topicId');
//       return ForumModel.fromJson(response.data);
//     } catch (e) {
//       debugPrint('Error fetching forum topic by id: $e');
//       rethrow;
//     }
//   }

//   Future<void> changeForumStatus(int topicId, bool fechado) async {
//     try {
//       debugPrint('Changing forum status: topicId=$topicId, fechado=$fechado');
//       final response = await _dio.put(
//         '/forum/$topicId/status',
//         data: {'fechado': fechado},
//       );
//       debugPrint('Response: ${response.data}');
//     } on DioException catch (e) {
//       debugPrint('Error: ${e.response?.data}');
//       throw _handleDioException(e);
//     }
//   }

//   //----------------------------------------------------------------
//   // CALENDAR API METHODS
//   //----------------------------------------------------------------

//   /// Buscar todos os eventos do calendário
//   Future<Map<String, dynamic>> getCalendarEvents({
//     int page = 1,
//     int perPage = 10,
//   }) async {
//     try {
//       Response response = await _dio.get(
//         'calendar/events',
//         queryParameters: {'page': page, 'perPage': perPage},
//       );
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   /// Criar um novo evento no calendário
//   Future<Map<String, dynamic>> createCalendarEvent(FormData data) async {
//     try {
//       Response response = await _dio.post('calendar/events', data: data);
//       return response.data;
//     } on DioException catch (e) {
//       debugPrint('Erro ao criar evento: ${e.message}');
//       debugPrint('Erro ao criar evento: ${e.response?.data}');
//       debugPrint('Erro ao criar evento: ${e.response?.statusCode}');
//       debugPrint('Erro ao criar evento: ${e.response?.statusMessage}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.uri}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.method}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.headers}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.data}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.queryParameters}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.connectTimeout}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.receiveTimeout}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.sendTimeout}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.followRedirects}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.maxRedirects}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.validateStatus}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.responseType}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.contentType}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.responseType}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.extra}');
//       debugPrint('Erro ao criar evento: ${e.requestOptions.baseUrl}');

//       throw _handleDioException(e);
//     }
//   }

//   /// Buscar um evento específico por ID
//   Future<Map<String, dynamic>> getCalendarEventById(int eventId) async {
//     try {
//       Response response = await _dio.get('calendar/events/$eventId');
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   /// Atualizar um evento do calendário
//   Future<Map<String, dynamic>> updateCalendarEvent(
//     int eventId,
//     FormData data,
//   ) async {
//     try {
//       Response response = await _dio.put(
//         'calendar/events/$eventId',
//         data: data,
//       );
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   /// Deletar um evento do calendário
//   Future<void> deleteCalendarEvent(int eventId) async {
//     try {
//       await _dio.delete('calendar/events/$eventId');
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   /// Participar de um evento
//   Future<Map<String, dynamic>> participateInEvent(int eventId) async {
//     try {
//       Response response = await _dio.post(
//         'calendar/events/$eventId/participate',
//       );
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   /// Sair de um evento
//   Future<void> leaveEvent(int eventId) async {
//     try {
//       await _dio.delete('calendar/events/$eventId/participate');
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   /// Buscar categorias de eventos
//   Future<List<Map<String, dynamic>>> getCalendarCategories() async {
//     try {
//       Response response = await _dio.get('calendar/categories');
//       return List<Map<String, dynamic>>.from(response.data);
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   /// Buscar eventos por data específica
//   Future<List<Map<String, dynamic>>> getCalendarEventsByDate(
//     DateTime date,
//   ) async {
//     try {
//       Response response = await _dio.get(
//         'calendar/events/by-date',
//         queryParameters: {'date': date.toIso8601String().split('T')[0]},
//       );
//       return List<Map<String, dynamic>>.from(response.data);
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   /// Buscar próximos eventos
//   Future<List<Map<String, dynamic>>> getUpcomingCalendarEvents({
//     int limit = 5,
//   }) async {
//     try {
//       Response response = await _dio.get(
//         'calendar/events/upcoming',
//         queryParameters: {'limit': limit},
//       );
//       return List<Map<String, dynamic>>.from(response.data);
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   //----------------------------------------------------------------
//   Exception _handleDioException(DioException e) {
//     if (e.error is ApiException) {
//       return e.error as ApiException;
//     } else {
//       return UnknownException('Erro desconhecido: ${e.message}');
//     }
//   }

//   /// Método para executar requisições com retry automático em caso de timeout
//   Future<T> _executeWithRetry<T>(
//     Future<T> Function() request, {
//     int maxRetries = 3,
//   }) async {
//     int attempt = 0;
//     while (attempt <= maxRetries) {
//       try {
//         return await request();
//       } on DioException catch (e) {
//         attempt++;

//         // Se for um erro de timeout, erro de rede, ou erro de conexão e ainda não atingiu o máximo de tentativas
//         if ((e.type == DioExceptionType.connectionTimeout ||
//                 e.type == DioExceptionType.receiveTimeout ||
//                 e.type == DioExceptionType.sendTimeout ||
//                 e.type == DioExceptionType.connectionError ||
//                 e.type == DioExceptionType.unknown) &&
//             attempt <= maxRetries) {
//           _logger.warning(
//             'Tentativa $attempt/${maxRetries + 1} falhou: ${e.type}. Tentando novamente em ${attempt * 3} segundos...',
//           );

//           // Aguarda um tempo crescente antes de tentar novamente (3s, 6s, 9s)
//           await Future.delayed(Duration(seconds: attempt * 3));
//           continue;
//         }

//         // Se não for um erro que permite retry ou já atingiu o máximo de tentativas, relança o erro
//         rethrow;
//       }
//     }

//     // Nunca deveria chegar aqui, mas por segurança
//     throw UnknownException('Falha após todas as tentativas');
//   }

//   //----------------------------------------------------------------
//   // FORUM COMMENTS METHODS
//   //----------------------------------------------------------------

//   /// Buscar comentários de um tópico do fórum com estrutura aninhada
//   Future<List<Map<String, dynamic>>> getForumComments(int topicId) async {
//     try {
//       Response response = await _dio.get('forum/$topicId/comment');
//       return List<Map<String, dynamic>>.from(response.data);
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   /// Buscar árvore completa de comentários de um tópico
//   Future<List<Map<String, dynamic>>> getForumCommentsTree(int topicId) async {
//     try {
//       Response response = await _dio.get('forum/$topicId/comments/tree');
//       return List<Map<String, dynamic>>.from(response.data);
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   /// Criar comentário ou resposta no fórum
//   Future<Map<String, dynamic>> createForumComment(
//     int topicId,
//     String content, {
//     int? parentId,
//   }) async {
//     try {
//       Map<String, dynamic> data = {'conteudo': content};
//       if (parentId != null) {
//         data['parent_id'] = parentId;
//       }

//       Response response = await _dio.post('forum/$topicId/comment', data: data);
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   /// Responder a um comentário específico
//   Future<Map<String, dynamic>> replyToForumComment(
//     int commentId,
//     String content,
//   ) async {
//     try {
//       Response response = await _dio.post(
//         'comment/$commentId/reply',
//         data: {'conteudo': content},
//       );
//       return response.data;
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }

//   /// Deletar um comentário do fórum
//   Future<void> deleteForumComment(int commentId) async {
//     try {
//       await _dio.delete('comment/$commentId');
//     } on DioException catch (e) {
//       throw _handleDioException(e);
//     }
//   }
// }
