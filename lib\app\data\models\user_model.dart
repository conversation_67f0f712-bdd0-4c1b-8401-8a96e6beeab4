import 'package:ibcb_desktop/app/data/models/role.dart';

class UserModel {
  final int? id;
  final String email;
  final String? nome;
  final String? imagemPerfil; // Pode ser nulo
  final DateTime? niver; // Pode ser nulo
  final DateTime criado;
  final String tipo_conta;
  List<Role>? cargos;

  UserModel({
    required this.id,
    required this.email,
    required this.nome,
    this.imagemPerfil,
    this.niver,
    required this.criado,
    required this.tipo_conta,
    this.cargos,
  });

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'],
      email: map['email'],
      nome: map['nome'],
      imagemPerfil: map['imagem_perfil'], // Pode ser nulo
      niver: map['niver'] != null ? DateTime.parse(map['niver']) : null,
      criado: DateTime.parse(map['criado']),
      tipo_conta: map['tipo_conta'],
      cargos:
          map['cargos'] != null && map['cargos'] is List
              ? (map['cargos'] as List)
                  .map(
                    (x) => x is Map<String, dynamic> ? Role.fromMap(x) : null,
                  )
                  .where((role) => role != null)
                  .cast<Role>()
                  .toList()
              : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'nome': nome,
      'imagem_perfil': imagemPerfil,
      'niver': niver?.toIso8601String(),
      'criado': criado.toIso8601String(),
      'tipo_conta': tipo_conta,
      'cargos': cargos?.map((x) => x.toMap()).toList(),
    };
  }

  bool hasRole(int roleId) {
    if (cargos == null) return false;
    return cargos!.any((role) => role.id == roleId);
  }
}
