import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import '../controllers/posts_controller.dart';
import '../../../data/models/posts_model.dart';

class PostsView extends GetView<PostsController> {
  const PostsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Gerenciar Posts'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        actions: [
          // Search
          Container(
            width: 300,
            margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: TextField(
              controller: controller.searchController,
              decoration: InputDecoration(
                hintText: 'Buscar posts...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Header with Create Button
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  'Posts do Sistema',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: controller.createNewPost,
                  icon: const Icon(Icons.add),
                  label: const Text('Novo Post'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
          ),

          // Posts List
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value && controller.posts.isEmpty) {
                return Center(
                  child: LoadingAnimationWidget.fourRotatingDots(
                    color: Theme.of(context).colorScheme.primary,
                    size: 40,
                  ),
                );
              }

              final posts = controller.filteredPosts;

              if (posts.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.article_outlined,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Nenhum post encontrado',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: controller.refreshPosts,
                child: NotificationListener<ScrollNotification>(
                  onNotification: (ScrollNotification scrollInfo) {
                    if (scrollInfo.metrics.pixels ==
                            scrollInfo.metrics.maxScrollExtent &&
                        !controller.isLoadingMore.value) {
                      controller.loadMorePosts();
                    }
                    return true;
                  },
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      // Determinar número de colunas baseado na largura da tela
                      int crossAxisCount = 3;
                      if (constraints.maxWidth < 800) {
                        crossAxisCount = 2;
                      } else if (constraints.maxWidth < 1200) {
                        crossAxisCount = 3;
                      } else {
                        crossAxisCount = 4;
                      }

                      return GridView.builder(
                        padding: const EdgeInsets.all(16),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: crossAxisCount,
                          crossAxisSpacing: 16,
                          mainAxisSpacing: 16,
                          childAspectRatio: 0.75, // Proporção width/height
                        ),
                        itemCount:
                            posts.length +
                            (controller.hasMoreData.value ? 1 : 0),
                        itemBuilder: (context, index) {
                          if (index == posts.length) {
                            // Load more indicator
                            return Obx(
                              () =>
                                  controller.isLoadingMore.value
                                      ? Card(
                                        child: Center(
                                          child:
                                              LoadingAnimationWidget.fourRotatingDots(
                                                color:
                                                    Theme.of(
                                                      context,
                                                    ).colorScheme.primary,
                                                size: 30,
                                              ),
                                        ),
                                      )
                                      : const SizedBox(),
                            );
                          }

                          return _PostGridCard(post: posts[index]);
                        },
                      );
                    },
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}

class _PostGridCard extends StatelessWidget {
  final PostsModel post;

  const _PostGridCard({required this.post});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PostsController>();

    return Card(
      elevation: 2,
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () => controller.viewPostDetails(post.id!),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with user info
            Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundImage:
                        post.user.imagemPerfil != null
                            ? CachedNetworkImageProvider(
                              post.user.imagemPerfil!,
                            )
                            : null,
                    child:
                        post.user.imagemPerfil == null
                            ? Text(
                              post.user.nome?.substring(0, 1).toUpperCase() ??
                                  '?',
                              style: const TextStyle(fontSize: 12),
                            )
                            : null,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          post.user.nome ?? 'Usuário',
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          _formatDate(post.criado),
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    iconSize: 16,
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          controller.editPost(post.id!);
                          break;
                        case 'delete':
                          controller.deletePost(post.id!);
                          break;
                        case 'view':
                          controller.viewPostDetails(post.id!);
                          break;
                      }
                    },
                    itemBuilder:
                        (context) => [
                          const PopupMenuItem(
                            value: 'view',
                            child: Row(
                              children: [
                                Icon(Icons.visibility, size: 16),
                                SizedBox(width: 8),
                                Text('Ver', style: TextStyle(fontSize: 12)),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, size: 16),
                                SizedBox(width: 8),
                                Text('Editar', style: TextStyle(fontSize: 12)),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, color: Colors.red, size: 16),
                                SizedBox(width: 8),
                                Text(
                                  'Excluir',
                                  style: TextStyle(
                                    color: Colors.red,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                  ),
                ],
              ),
            ),

            // Content or Image
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Post content
                    if (post.conteudo != null && post.conteudo!.isNotEmpty)
                      Expanded(
                        child: Text(
                          post.conteudo!,
                          style: const TextStyle(fontSize: 12),
                          maxLines: 4,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                    // Post image
                    if (post.imagem != null && post.imagem!.isNotEmpty)
                      Expanded(
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(6),
                          child: CachedNetworkImage(
                            imageUrl: post.imagem!,
                            width: double.infinity,
                            fit: BoxFit.cover,
                            placeholder:
                                (context, url) => Container(
                                  color: Colors.grey[300],
                                  child: const Center(
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                    ),
                                  ),
                                ),
                            errorWidget:
                                (context, url, error) => Container(
                                  color: Colors.grey[300],
                                  child: const Center(
                                    child: Icon(Icons.error, size: 20),
                                  ),
                                ),
                          ),
                        ),
                      ),

                    // If no content and no image, show placeholder
                    if ((post.conteudo == null || post.conteudo!.isEmpty) &&
                        (post.imagem == null || post.imagem!.isEmpty))
                      const Expanded(
                        child: Center(
                          child: Icon(
                            Icons.article_outlined,
                            size: 32,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            // Footer with stats
            Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Icon(Icons.favorite, size: 14, color: Colors.red[400]),
                  const SizedBox(width: 4),
                  Text('${post.likes}', style: const TextStyle(fontSize: 11)),
                  const SizedBox(width: 12),
                  Icon(Icons.comment, size: 14, color: Colors.blue[400]),
                  const SizedBox(width: 4),
                  Text(
                    '${post.comentarios.length}',
                    style: const TextStyle(fontSize: 11),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => controller.likePost(post.id!),
                    icon: const Icon(Icons.favorite_border),
                    iconSize: 16,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    tooltip: 'Curtir',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'Agora';
    }
  }
}
