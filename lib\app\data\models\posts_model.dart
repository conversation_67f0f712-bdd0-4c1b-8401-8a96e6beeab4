import 'package:ibcb_desktop/app/data/models/comments_model.dart';
import 'package:ibcb_desktop/app/data/models/user_model.dart';

class PostsModel {
  UserModel user;
  int? id;
  final int userId;
  // final String autor;
  // final String autorImagem;
  final String? conteudo;
  final String? imagem;
  final int likes;
  final DateTime criado;
  final List<CommentsModel> comentarios;

  PostsModel({
    required this.user,
    this.id,
    required this.userId,
    // required this.autor,
    // required this.autorImagem,
    this.conteudo,
    this.imagem,
    required this.likes,
    required this.criado,
    required this.comentarios,
  });

  factory PostsModel.fromMap(Map<String, dynamic> map) {
    return PostsModel(
      id: map['id'],
      userId: map['user_id'],
      // autor: map['autor'],
      // autorImagem: map['autor_imagem'],
      conteudo: map['conteudo'],
      imagem: map['imagem'],
      likes: map['likes'] ?? 0,
      criado: DateTime.parse(map['criado']),
      comentarios: List<CommentsModel>.from(
        map['comentarios']?.map((x) => CommentsModel.fromMap(x)) ?? [],
      ),
      user: UserModel.fromMap(map['user']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'conteudo': conteudo,
      'imagem': imagem,
      'likes': likes,
      'criado': criado.toIso8601String(),
      'user': user.toMap(),
    };
  }
}
