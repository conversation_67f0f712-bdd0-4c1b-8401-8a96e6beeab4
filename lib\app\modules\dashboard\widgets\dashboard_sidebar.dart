import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/dashboard_controller.dart';

class DashboardSidebar extends GetView<DashboardController> {
  const DashboardSidebar({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          // Logo/Header
          Container(
            height: 64,
            padding: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.church,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'IBCB Desktop',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    Text(
                      'Gerenciamento',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Menu Items
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: controller.menuItems.length,
              itemBuilder: (context, index) {
                final menuItem = controller.menuItems[index];
                final canAccess = controller.canAccessRoute(menuItem);
                
                return Obx(
                  () => Container(
                    margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                    child: ListTile(
                      leading: Icon(
                        _getIconData(menuItem.icon),
                        color: canAccess
                            ? (controller.selectedIndex.value == index
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.onSurface.withOpacity(0.7))
                            : Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                      ),
                      title: Text(
                        menuItem.title,
                        style: TextStyle(
                          color: canAccess
                              ? (controller.selectedIndex.value == index
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.onSurface)
                              : Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                          fontWeight: controller.selectedIndex.value == index
                              ? FontWeight.w600
                              : FontWeight.normal,
                        ),
                      ),
                      selected: controller.selectedIndex.value == index,
                      selectedTileColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      onTap: canAccess ? () => controller.selectMenuItem(index) : null,
                    ),
                  ),
                );
              },
            ),
          ),
          
          // Footer
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const Divider(),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Versão 1.0.0',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'dashboard':
        return Icons.dashboard_outlined;
      case 'people':
        return Icons.people_outline;
      case 'article':
        return Icons.article_outlined;
      case 'forum':
        return Icons.forum_outlined;
      case 'event':
        return Icons.event_outlined;
      case 'calendar_today':
        return Icons.calendar_today_outlined;
      case 'school':
        return Icons.school_outlined;
      case 'attach_money':
        return Icons.attach_money_outlined;
      default:
        return Icons.circle_outlined;
    }
  }
}
