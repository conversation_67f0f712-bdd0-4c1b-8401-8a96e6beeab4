import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';

class WindowsConfigs {
  static Future<void> setConfigs() async {
    if (!kIsWeb && Platform.isWindows) {
      await windowManager.setSize(const Size(800, 700)); // Tamanho inicial
      await windowManager.setMinimumSize(
        const Size(700, 700),
      ); // <PERSON><PERSON><PERSON> mínimo
      await windowManager.setPosition(
        const Offset(100, 100),
      ); // Posição inicial na tela
      await windowManager.show(); // Mostra a janela
      await windowManager.focus(); // Garante o foco na janela
    }
  }
}
