import 'package:ibcb_desktop/app/data/providers/base/base_api_provider.dart';
import '../../models/formacao/estudo_model.dart';

class EstudoApiProvider extends BaseApiProvider {
  Future<List<EstudoModel>> getEstudos({int? tipoFormacaoId}) async {
    return executeWithRetry(() async {
      String endpoint = '/estudos';
      Map<String, dynamic>? queryParams;

      if (tipoFormacaoId != null) {
        queryParams = {'tipo_formacao_id': tipoFormacaoId};
      }

      final response = await dio.get(endpoint, queryParameters: queryParams);
      final List<dynamic> data = response.data;
      return data.map((json) => EstudoModel.fromJson(json)).toList();
    });
  }

  Future<EstudoModel> getEstudo(int id) async {
    return executeWithRetry(() async {
      final response = await dio.get('/estudos/$id');
      return EstudoModel.fromJson(response.data);
    });
  }

  Future<EstudoModel> createEstudo(EstudoModel estudo) async {
    return executeWithRetry(() async {
      final response = await dio.post('/estudos', data: estudo.toJson());
      return EstudoModel.fromJson(response.data);
    });
  }

  Future<EstudoModel> updateEstudo(int id, EstudoModel estudo) async {
    return executeWithRetry(() async {
      final response = await dio.put('/estudos/$id', data: estudo.toJson());
      return EstudoModel.fromJson(response.data);
    });
  }

  Future<void> deleteEstudo(int id) async {
    return executeWithRetry(() async {
      await dio.delete('/estudos/$id');
    });
  }

  Future<void> avaliarEstudo(
    int estudoId,
    double nota,
    String? comentario,
  ) async {
    return executeWithRetry(() async {
      await dio.post(
        '/estudos/$estudoId/avaliar',
        data: {'nota': nota, 'comentario': comentario},
      );
    });
  }

  Future<void> marcarProgresso(
    int estudoId,
    bool aprovado,
    double? notaFinal,
  ) async {
    return executeWithRetry(() async {
      await dio.post(
        '/estudos/$estudoId/progresso',
        data: {'aprovado': aprovado, 'nota_final': notaFinal},
      );
    });
  }

  Future<List<EstudoModel>> getEstudosByTema(int temaId) async {
    return executeWithRetry(() async {
      final response = await dio.get(
        '/estudos',
        queryParameters: {'tema_id': temaId},
      );
      final List<dynamic> data = response.data;
      return data.map((json) => EstudoModel.fromJson(json)).toList();
    });
  }
}
