class ApiException implements Exception {
  final String mensagem;
  ApiException(this.mensagem);

  @override
  String toString() => 'ApiException: $mensagem';
}

class NetworkException extends ApiException {
  NetworkException(String mensagem) : super(mensagem);
}

class UnauthorizedException extends ApiException {
  UnauthorizedException(String mensagem) : super(mensagem);
}

class NotFoundException extends ApiException {
  NotFoundException(String mensagem) : super(mensagem);
}

class BadRequestException extends ApiException {
  BadRequestException(String mensagem) : super(mensagem);
}

class InternalServerException extends ApiException {
  InternalServerException(String mensagem) : super(mensagem);
}

class UnknownException extends ApiException {
  UnknownException(String mensagem) : super(mensagem);
}

class InvalidCredentialsException extends ApiException {
  InvalidCredentialsException(String mensagem) : super(mensagem);
}
