import 'package:ibcb_desktop/app/data/models/meta.dart';
import 'package:ibcb_desktop/app/data/models/posts_model.dart';

class PostsPageResponse {
  final Meta meta;
  final List<PostsModel> data;

  PostsPageResponse({required this.meta, required this.data});

  factory PostsPageResponse.fromJson(Map<String, dynamic> json) {
    return PostsPageResponse(
      meta: Meta.fromJson(json['meta']),
      data: (json['data'] as List).map((i) => PostsModel.fromMap(i)).toList(),
    );
  }
}
