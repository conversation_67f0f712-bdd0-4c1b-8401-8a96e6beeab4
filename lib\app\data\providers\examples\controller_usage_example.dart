import 'package:get/get.dart';
import 'package:ibcb_desktop/app/data/providers/api.dart';
import 'package:ibcb_desktop/app/data/providers/user/user_api_provider.dart';
import 'package:ibcb_desktop/app/data/providers/forum/forum_api_provider.dart';

/// Exemplo de controller mostrando diferentes formas de usar a nova arquitetura
class ControllerUsageExample extends GetxController {
  // ================================================================
  // FORMA 1: Usando a API modular (recomendado para código novo)
  // ================================================================

  final UserApiProvider _userApi = Get.find<UserApiProvider>();
  final ForumApiProvider _forumApi = Get.find<ForumApiProvider>();

  /// Exemplo usando providers específicos diretamente
  Future<void> getUserWithSpecificProvider() async {
    try {
      // Usar diretamente o provider de usuários
      await _userApi.getUser();
      // Processar resultado...
    } catch (e) {
      // Tratar erro...
    }
  }

  Future<void> getForumTopicsWithSpecificProvider() async {
    try {
      // Usar diretamente o provider do fórum
      await _forumApi.fetchForumTopics(page: 1, perPage: 10);
      // Processar tópicos...
    } catch (e) {
      // Tratar erro...
    }
  }

  // ================================================================
  // FORMA 2: Usando a API tradicional (compatibilidade total)
  // ================================================================

  final Api _api = Get.find<Api>();

  /// Exemplo usando a classe Api tradicional (ainda funciona)
  Future<void> getUserWithTraditionalApi() async {
    try {
      // Funciona exatamente como antes
      await _api.getUser();
      // Processar resultado...
    } catch (e) {
      // Tratar erro...
    }
  }

  Future<void> getForumTopicsWithTraditionalApi() async {
    try {
      // Funciona exatamente como antes
      await _api.fetchForumTopics(page: 1, perPage: 10);
      // Processar tópicos...
    } catch (e) {
      // Tratar erro...
    }
  }

  // ================================================================
  // FORMA 3: Usando a API modular através da classe Api
  // ================================================================

  /// Exemplo usando providers através da classe Api
  Future<void> useModularThroughApi() async {
    try {
      // Acesso direto aos providers através da Api
      await _api.user.getUser();
      await _api.forum.fetchForumTopics();
      await _api.calendar.getCalendarEvents();

      // Processar dados...
    } catch (e) {
      // Tratar erro...
    }
  }

  // ================================================================
  // EXEMPLO DE MIGRAÇÃO GRADUAL
  // ================================================================

  /// Este é um exemplo de como migrar gradualmente um controller existente
  /// Você pode começar usando a forma tradicional e ir migrando aos poucos
  Future<void> gradualMigrationExample() async {
    try {
      // Método antigo (ainda funciona)
      await _api.getUser();

      // Método novo usando provider específico
      await _forumApi.fetchForumTopics();

      // Método novo usando provider através da Api
      await _api.calendar.getCalendarEvents();

      // Processar dados...
    } catch (e) {
      // Tratar erro...
    }
  }
}
