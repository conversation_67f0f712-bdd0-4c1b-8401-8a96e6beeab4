import 'package:get/get.dart';
import '../../../data/services/auth/auth_service.dart';
import '../../../data/services/storage/storage_service.dart';
import '../../../routes/pages.dart';
import '../../../core/logger/app_logger.dart';

class SplashController extends GetxController {
  static final _logger = AppLogger.logger;

  final AuthService _authService = Get.find<AuthService>();
  // final StorageService _storageService = Get.find<StorageService>();

  @override
  void onInit() {
    super.onInit();
    _checkAuthentication();
  }

  Future<void> _checkAuthentication() async {
    _logger.debug('Checking authentication status...');

    // Aguarda um pouco para mostrar a splash screen
    // await Future.delayed(const Duration(seconds: 2));

    // String? token = _storageService.token;

    // if (token != null && token.isNotEmpty) {
    _logger.info('Token found, attempting to get user...');
    try {
      await _authService.getUser();
      if (_authService.user.value != null) {
        _logger.info('User authenticated, redirecting to dashboard');
        Get.offAllNamed(Routes.dashboard);
      }
      // else {
      //   _logger.warning('Token invalid, redirecting to login');
      //   Get.offAllNamed(Routes.login);
      // }
    } catch (e) {
      _logger.error('Error validating token: $e');
      Get.offAllNamed(Routes.login);
    }
  }

  // else {
  //   _logger.info('No token found, redirecting to login');
  //   Get.offAllNamed(Routes.login);
  // }
}

// }
