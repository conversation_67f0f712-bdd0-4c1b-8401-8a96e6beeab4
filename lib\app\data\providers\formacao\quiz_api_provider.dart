import 'package:ibcb_desktop/app/data/providers/base/base_api_provider.dart';
import 'package:flutter/material.dart';
import '../../models/formacao/quiz_model.dart';

class QuizApiProvider extends BaseApiProvider {
  Future<List<QuizModel>> getQuizzes(int estudoId) async {
    return executeWithRetry(() async {
      final response = await dio.get('/estudos/$estudoId/quizzes');
      final List<dynamic> data = response.data;
      return data.map((json) => QuizModel.fromJson(json)).toList();
    });
  }

  Future<QuizModel> getQuiz(int id) async {
    return executeWithRetry(() async {
      final response = await dio.get('/quizzes/$id');
      return QuizModel.fromJson(response.data);
    });
  }

  Future<QuizModel> createQuiz(int estudoId, QuizModel quiz) async {
    return executeWithRetry(() async {
      final response = await dio.post(
        '/estudos/$estudoId/quizzes',
        data: quiz.toJson(),
      );
      return QuizModel.fromJson(response.data);
    });
  }

  Future<QuizModel> updateQuiz(int id, QuizModel quiz) async {
    return executeWithRetry(() async {
      final response = await dio.put('/quizzes/$id', data: quiz.toJson());
      return QuizModel.fromJson(response.data);
    });
  }

  Future<void> deleteQuiz(int id) async {
    return executeWithRetry(() async {
      await dio.delete('/quizzes/$id');
    });
  }

  Future<QuizAlternativaModel> createAlternativa(
    QuizAlternativaModel alternativa,
  ) async {
    return executeWithRetry(() async {
      debugPrint('Enviando alternativa para API: ${alternativa.toJson()}');
      debugPrint('URL: /quizzes/${alternativa.quizId}/alternativas');

      final response = await dio.post(
        '/quizzes/${alternativa.quizId}/alternativas',
        data: alternativa.toJson(),
      );

      debugPrint('Resposta da API: ${response.data}');
      return QuizAlternativaModel.fromJson(response.data);
    });
  }

  Future<void> deleteAlternativa(int id) async {
    return executeWithRetry(() async {
      await dio.delete('/alternativas/$id');
    });
  }

  Future<RespostaQuizUsuarioModel> responderQuiz(
    int quizId,
    String resposta,
  ) async {
    return executeWithRetry(() async {
      final response = await dio.post(
        '/quizzes/$quizId/responder',
        data: {'resposta': resposta},
      );
      return RespostaQuizUsuarioModel.fromJson(response.data);
    });
  }

  Future<Map<String, dynamic>> getResultados(int estudoId) async {
    return executeWithRetry(() async {
      final response = await dio.get('/estudos/$estudoId/quiz-resultados');
      return response.data;
    });
  }
}
