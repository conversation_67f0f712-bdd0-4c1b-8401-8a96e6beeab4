# Enhanced Logger System for IBCB App

This enhanced logger system provides beautiful, structured, and highly readable console output for Flutter development, with advanced features for debugging, monitoring, and performance tracking.

## Features

### 🎨 Visual Enhancements
- **Colored output** with ANSI color codes for different log levels
- **Beautiful borders** and visual separators using Unicode box-drawing characters
- **Emoji indicators** for quick visual identification of log levels
- **Structured layout** with clear sections for message, error, and stack trace
- **Automatic line wrapping** for long messages
- **Consistent spacing** and formatting

### 📊 Log Levels with Visual Distinction
- 🔍 **TRACE** (White) - Detailed tracing information
- 🐛 **DEBUG** (Cyan) - Debug information for development
- ℹ️ **INFO** (Green) - General information messages
- ⚠️ **WARNING** (Yellow) - Warning messages
- ❌ **ERROR** (Red) - Error messages
- 💀 **FATAL** (Magenta) - Fatal error messages

### 🏗️ Contextual Information
- **Module/Component tagging** for organized logging
- **Precise timestamps** with millisecond accuracy (HH:MM:SS.mmm)
- **Method entry/exit tracking** for debugging flow
- **Enhanced stack traces** with clean formatting
- **Performance measurements** with duration tracking

### 🚀 Specialized Logging Methods
- **API Request/Response logging** with status codes and data
- **Firebase operation logging** with collection and document info
- **User action tracking** with context
- **Performance monitoring** with duration and details
- **Data structure logging** with pretty-printing
- **Method flow tracking** with entry/exit points

## Usage Examples

### Basic Logging
```dart
import 'package:ibcb_desktop/app/core/logger/app_logger.dart';

// Simple logging
AppLogger.logger.debug('Debug message');
AppLogger.logger.info('Info message');
AppLogger.logger.warning('Warning message');
AppLogger.logger.error('Error message');
```

### Module-Specific Logging
```dart
// Create a logger for a specific module
final logger = ModuleLogger.create('Authentication');

logger.info('User authentication started');
logger.error('Authentication failed', error, stackTrace);
```

### Method Flow Tracking
```dart
final logger = ModuleLogger.create('UserService');

Future<User> loadUser(String userId) async {
  logger.methodEntry('loadUser', {'userId': userId});
  
  try {
    // Your code here
    final user = await fetchUserFromApi(userId);
    logger.methodExit('loadUser', 'success');
    return user;
  } catch (e, stackTrace) {
    logger.error('Failed to load user', e, stackTrace);
    logger.methodExit('loadUser', 'error');
    rethrow;
  }
}
```

### API Logging
```dart
final logger = ModuleLogger.create('API');

// Log API requests and responses
logger.apiRequest('POST', '/api/users', {'name': 'John', 'email': '<EMAIL>'});
logger.apiResponse('POST', '/api/users', 201, {'id': 123, 'name': 'John'});
```

### Firebase Operations
```dart
final logger = ModuleLogger.create('Database');

logger.firebaseOperation('CREATE', 'users', 'user123');
logger.firebaseOperation('READ', 'posts');
logger.firebaseOperation('UPDATE', 'users', 'user123');
```

### Performance Monitoring
```dart
final logger = ModuleLogger.create('Performance');

final stopwatch = Stopwatch()..start();
await performSlowOperation();
stopwatch.stop();

logger.performance('slow_operation', stopwatch.elapsed, 'database query');
```

### User Action Tracking
```dart
final logger = ModuleLogger.create('UI');

logger.userAction('login', {'userId': '123', 'method': 'google'});
logger.userAction('navigate', {'from': 'home', 'to': 'profile'});
```

### Data Structure Logging
```dart
final logger = ModuleLogger.create('Data');

final complexData = {
  'user': {'id': 123, 'name': 'John'},
  'preferences': {'theme': 'dark', 'notifications': true},
  'tags': ['developer', 'flutter']
};

logger.data('User Data', complexData);
```

## Console Output Example

```
┌┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
│ 14:32:15.123 ℹ️ INFO [Authentication]
├┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈
│ User authentication started
└┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄

┌┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
│ 14:32:15.456 🌐 INFO [API]
├┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈┈
│ 🌐 API POST /api/users
│ Data: {name: John, email: <EMAIL>}
└┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
```

## Configuration

### Release Mode Filtering
In release mode, only warnings and errors are logged to reduce performance impact and log noise.

### Module Organization
Use `ModuleLogger.create('ModuleName')` to create loggers for different parts of your application:
- `Authentication`
- `Database`
- `UI`
- `API`
- `Performance`
- etc.

## Integration in Controllers

```dart
class UserController extends GetxController {
  static final _logger = ModuleLogger.create('UserController');

  @override
  void onInit() {
    super.onInit();
    _logger.methodEntry('onInit');
    // Your initialization code
    _logger.methodExit('onInit');
  }

  Future<void> loadUserData() async {
    _logger.methodEntry('loadUserData');
    
    try {
      final stopwatch = Stopwatch()..start();
      
      // Your code here
      
      stopwatch.stop();
      _logger.performance('loadUserData', stopwatch.elapsed);
      _logger.methodExit('loadUserData', 'success');
    } catch (e, stackTrace) {
      _logger.error('Failed to load user data', e, stackTrace);
      _logger.methodExit('loadUserData', 'error');
      rethrow;
    }
  }
}
```

This enhanced logger system will significantly improve your development experience by providing clear, structured, and visually appealing console output that makes debugging and monitoring much easier.
