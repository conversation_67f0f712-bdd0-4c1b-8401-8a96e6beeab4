import 'package:flutter/material.dart';
import 'package:ibcb_desktop/app/data/models/forum_comments_model.dart';
import 'package:ibcb_desktop/app/data/models/user_model.dart';

class ForumModel {
  final int id;
  final int userId;
  final UserModel user;
  final String titulo;
  final String conteudo;
  final String? imagem;
  final bool fechado;
  int numComents;
  final DateTime criado;
  final List<ForumCommentModel> comments;

  ForumModel({
    required this.id,
    required this.userId,
    required this.user,
    required this.titulo,
    required this.conteudo,
    this.imagem,
    required this.fechado,
    required this.numComents,
    required this.criado,
    required this.comments,
  });
  factory ForumModel.fromJson(Map<String, dynamic> json) {
    try {
      // Processar forum_respostas com tratamento de erro
      List<ForumCommentModel> commentList = [];
      if (json['forum_respostas'] != null && json['forum_respostas'] is List) {
        var list = json['forum_respostas'] as List;
        for (var item in list) {
          try {
            commentList.add(ForumCommentModel.fromMap(item));
          } catch (e) {
            debugPrint('Erro ao processar comentário: $e');
            // Continua processando outros comentários
          }
        }
      }

      // Tratamento especial para o campo imagem
      String? imagem;
      if (json['imagem'] != null && json['imagem'] is String) {
        imagem = json['imagem'];
      }

      return ForumModel(
        id: json['id'] ?? 0,
        userId: json['user_id'] ?? 0,
        user: UserModel.fromMap(json['user']),
        titulo: json['titulo']?.toString() ?? '',
        conteudo: json['conteudo']?.toString() ?? '',
        imagem: imagem,
        fechado: json['fechado'] == 1,
        numComents: json['num_coments'] ?? 0,
        criado: DateTime.parse(json['criado']),
        comments: commentList,
      );
    } catch (e) {
      debugPrint('Erro ao converter ForumModel: $e');
      rethrow;
    }
  }
}
