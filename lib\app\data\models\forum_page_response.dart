import 'package:ibcb_desktop/app/data/models/forum_model.dart';
import 'package:ibcb_desktop/app/data/models/meta.dart';

class ForumPageResponse {
  final Meta meta;
  final List<ForumModel> data;

  ForumPageResponse({required this.meta, required this.data});

  factory ForumPageResponse.fromJson(Map<String, dynamic> json) {
    return ForumPageResponse(
      meta: Meta.fromJson(json['meta']),
      data: (json['data'] as List).map((i) => ForumModel.fromJson(i)).toList(),
    );
  }
}
