class TipoFormacaoModel {
  final int id;
  final String nome;
  final String? descricao;
  final int? ordem;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  TipoFormacaoModel({
    required this.id,
    required this.nome,
    this.descricao,
    this.ordem,
    this.createdAt,
    this.updatedAt,
  });

  factory TipoFormacaoModel.fromJson(Map<String, dynamic> json) {
    return TipoFormacaoModel(
      id: json['id'] ?? 0,
      nome: json['nome'] ?? '',
      descricao: json['descricao'],
      ordem: json['ordem'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'descricao': descricao,
      'ordem': ordem,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}
