import 'package:get/get.dart';
import 'package:ibcb_desktop/app/data/models/role.dart';
import 'package:ibcb_desktop/app/data/models/user_model.dart';
import 'package:ibcb_desktop/app/data/services/user/user_repository.dart';


class UserService extends GetxService {
  final UserRepository _repository;

  UserService(this._repository);

  Future<List<UserModel>> fetchUsers() {
    return _repository.fetchUsers();
  }

  Future<List<Role>> fetchCargos() {
    return _repository.fetchCargos();
  }

  Future<void> updateUserCargos(int userId, List<int> cargoIds) {
    return _repository.updateUserCargos(userId, cargoIds);
  }
}
