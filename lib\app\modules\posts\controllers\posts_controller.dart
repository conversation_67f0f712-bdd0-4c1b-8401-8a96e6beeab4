import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:ibcb_desktop/app/data/models/posts_model.dart';
import 'package:ibcb_desktop/app/data/providers/post/post_api_provider.dart';
import 'package:ibcb_desktop/app/core/logger/app_logger.dart';
import 'package:ibcb_desktop/app/routes/pages.dart';
import 'package:dio/dio.dart' as dio;

class PostsController extends GetxController {
  final PostApiProvider _postProvider = Get.find<PostApiProvider>();

  var posts = <PostsModel>[].obs;
  var isLoading = false.obs;
  var isLoadingMore = false.obs;
  var currentPage = 1.obs;
  var totalPages = 1.obs;
  var hasMoreData = true.obs;

  final TextEditingController contentController = TextEditingController();
  final TextEditingController searchController = TextEditingController();

  var searchQuery = ''.obs;
  var selectedImage = Rxn<String>();

  @override
  void onInit() {
    super.onInit();
    AppLogger.logger.debug('PostsController initialized');
    loadPosts();

    // Setup search debounce
    searchController.addListener(() {
      searchQuery.value = searchController.text;
    });
  }

  @override
  void onClose() {
    contentController.dispose();
    searchController.dispose();
    super.onClose();
  }

  Future<void> loadPosts({bool refresh = false}) async {
    if (refresh) {
      currentPage.value = 1;
      posts.clear();
      hasMoreData.value = true;
    }

    if (!hasMoreData.value && !refresh) return;

    isLoading(refresh ? true : isLoadingMore.value);
    if (!refresh) isLoadingMore(true);

    try {
      AppLogger.logger.info('Loading posts - Page: ${currentPage.value}');

      final response = await _postProvider.getPosts(
        page: currentPage.value,
        perPage: 10,
      );

      if (response['data'] != null) {
        final newPosts =
            (response['data'] as List)
                .map((post) => PostsModel.fromMap(post))
                .toList();

        if (refresh) {
          posts.value = newPosts;
        } else {
          posts.addAll(newPosts);
        }

        totalPages.value = response['meta']?['last_page'] ?? 1;
        hasMoreData.value = newPosts.length >= 10;

        AppLogger.logger.info('Successfully loaded ${newPosts.length} posts');
      } else {
        AppLogger.logger.warning('No posts found in response');
        if (refresh) {
          posts.clear();
        }
      }
    } catch (e) {
      AppLogger.logger.error('Erro ao carregar posts: $e');
      Get.snackbar('Erro', 'Erro ao carregar posts: $e');
    } finally {
      isLoading(false);
      isLoadingMore(false);
    }
  }

  Future<void> loadMorePosts() async {
    if (isLoadingMore.value || !hasMoreData.value) return;

    currentPage.value++;
    await loadPosts();
  }

  List<PostsModel> get filteredPosts {
    if (searchQuery.value.isEmpty) {
      return posts.toList();
    }

    return posts.where((post) {
      final content = post.conteudo?.toLowerCase() ?? '';
      final userName = post.user.nome?.toLowerCase() ?? '';
      final query = searchQuery.value.toLowerCase();

      return content.contains(query) || userName.contains(query);
    }).toList();
  }

  Future<void> createPost() async {
    if (contentController.text.trim().isEmpty) {
      Get.snackbar('Erro', 'O conteúdo do post não pode estar vazio');
      return;
    }

    try {
      isLoading(true);

      final formData = dio.FormData.fromMap({
        'conteudo': contentController.text.trim(),
      });

      if (selectedImage.value != null) {
        formData.files.add(
          MapEntry(
            'imagem',
            await dio.MultipartFile.fromFile(selectedImage.value!),
          ),
        );
      }

      await _postProvider.createPost(formData);

      // Limpar formulário
      contentController.clear();
      selectedImage.value = null;

      // Recarregar posts
      await loadPosts(refresh: true);

      Get.snackbar('Sucesso', 'Post criado com sucesso');
      Get.back(); // Voltar para a lista
    } catch (e) {
      AppLogger.logger.error('Erro ao criar post: $e');
      Get.snackbar('Erro', 'Erro ao criar post: $e');
    } finally {
      isLoading(false);
    }
  }

  Future<void> updatePost(int postId) async {
    if (contentController.text.trim().isEmpty) {
      Get.snackbar('Erro', 'O conteúdo do post não pode estar vazio');
      return;
    }

    try {
      isLoading(true);

      final formData = dio.FormData.fromMap({
        'conteudo': contentController.text.trim(),
      });

      if (selectedImage.value != null) {
        formData.files.add(
          MapEntry(
            'imagem',
            await dio.MultipartFile.fromFile(selectedImage.value!),
          ),
        );
      }

      // Chama a API de update (que pode não retornar dados completos)
      await _postProvider.updatePost(postId, formData);

      // Recarrega a lista de posts para garantir dados atualizados
      await loadPosts(refresh: true);

      Get.snackbar('Sucesso', 'Post atualizado com sucesso');
      Get.back(); // Voltar para a lista
    } catch (e) {
      AppLogger.logger.error('Erro ao atualizar post: $e');
      Get.snackbar('Erro', 'Erro ao atualizar post: $e');
    } finally {
      isLoading(false);
    }
  }

  Future<void> deletePost(int postId) async {
    try {
      final confirm = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('Confirmar Exclusão'),
          content: const Text('Tem certeza que deseja excluir este post?'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('Cancelar'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('Excluir'),
            ),
          ],
        ),
      );

      if (confirm == true) {
        isLoading(true);
        await _postProvider.deletePost(postId);
        posts.removeWhere((post) => post.id == postId);
        Get.snackbar('Sucesso', 'Post excluído com sucesso');
      }
    } catch (e) {
      AppLogger.logger.error('Erro ao excluir post: $e');
      Get.snackbar('Erro', 'Erro ao excluir post: $e');
    } finally {
      isLoading(false);
    }
  }

  void selectImage(String imagePath) {
    selectedImage.value = imagePath;
  }

  void removeImage() {
    selectedImage.value = null;
  }

  void editPost(int postId) {
    final post = posts.firstWhere((p) => p.id == postId);
    contentController.text = post.conteudo ?? '';
    selectedImage.value = post.imagem;
    Get.toNamed(Routes.postEdit, parameters: {'id': postId.toString()});
  }

  void viewPostDetails(int postId) {
    Get.toNamed(Routes.postView, parameters: {'id': postId.toString()});
  }

  void createNewPost() {
    contentController.clear();
    selectedImage.value = null;
    Get.toNamed(Routes.postCreate);
  }

  Future<void> refreshPosts() async {
    await loadPosts(refresh: true);
  }

  Future<void> likePost(int postId) async {
    try {
      AppLogger.logger.info('Toggling like for post $postId');
      // TODO: Implementar like/unlike na API
      // await _postProvider.toggleLike(postId);

      // Atualizar localmente (temporário)
      final index = posts.indexWhere((post) => post.id == postId);
      if (index != -1) {
        // Simular toggle like localmente até implementar API
        // Para agora, só vamos mostrar feedback visual
        Get.snackbar(
          'Curtida',
          'Post curtido!',
          duration: const Duration(seconds: 1),
        );
      }
    } catch (e) {
      AppLogger.logger.error('Erro ao curtir post: $e');
      Get.snackbar('Erro', 'Erro ao curtir post: $e');
    }
  }

  // Get post by ID
  PostsModel? getPostById(int id) {
    return posts.firstWhereOrNull((post) => post.id == id);
  }
}
