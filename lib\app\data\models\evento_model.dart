class EventoModel {
  int? id;
  int? user_id;
  String imagem;
  DateTime? data_expiracao;
  int? visualizacoes;
  EventoModel({
    this.id,
    this.user_id,
    required this.imagem,
    this.data_expiracao,
    this.visualizacoes,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': user_id,
      'imagem': imagem,
      'data_expiracao': data_expiracao?.millisecondsSinceEpoch,
      'visualizacoes': visualizacoes,
    };
  }

  factory EventoModel.fromMap(Map<String, dynamic> map) {
    return EventoModel(
      id: map['id']?.toInt(),
      user_id: map['user_id']?.toInt(),
      imagem: map['imagem'],
      data_expiracao: map['data_expiracao'],
      visualizacoes: map['visualizacoes']?.toInt(),
    );
  }
}
