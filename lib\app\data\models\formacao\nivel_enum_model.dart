class NivelEnumModel {
  final int id;
  final String codigo;
  final String nome;
  final String? descricao;
  final int ordem;
  final DateTime createdAt;
  final DateTime updatedAt;

  NivelEnumModel({
    required this.id,
    required this.codigo,
    required this.nome,
    this.descricao,
    required this.ordem,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NivelEnumModel.fromJson(Map<String, dynamic> json) {
    return NivelEnumModel(
      id: json['id'] ?? 0,
      codigo: json['codigo'] ?? '',
      nome: json['nome'] ?? '',
      descricao: json['descricao'],
      ordem: json['ordem'] ?? 0,
      createdAt: DateTime.parse(
        json['created_at'] ??
            json['createdAt'] ??
            DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updated_at'] ??
            json['updatedAt'] ??
            DateTime.now().toIso8601String(),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'codigo': codigo,
      'nome': nome,
      'descricao': descricao,
      'ordem': ordem,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  NivelEnumModel copyWith({
    int? id,
    String? codigo,
    String? nome,
    String? descricao,
    int? ordem,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return NivelEnumModel(
      id: id ?? this.id,
      codigo: codigo ?? this.codigo,
      nome: nome ?? this.nome,
      descricao: descricao ?? this.descricao,
      ordem: ordem ?? this.ordem,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
