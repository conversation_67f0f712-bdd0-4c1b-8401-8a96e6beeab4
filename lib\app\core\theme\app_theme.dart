import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:get/get.dart';

class ThemeService extends GetxService {
  final _box = GetStorage();
  final _key = 'isDarkMode';
  final _isDarkMode = false.obs;

  bool get isDarkMode => _isDarkMode.value;
  ThemeMode get theme => _isDarkMode.value ? ThemeMode.dark : ThemeMode.light;

  @override
  void onInit() {
    super.onInit();
    _loadThemeFromBox();
  }

  void _loadThemeFromBox() {
    _isDarkMode.value = _box.read(_key) ?? false;
  }

  void saveTheme(bool isDarkMode) {
    _isDarkMode.value = isDarkMode;
    _box.write(_key, isDarkMode);
  }

  void changeTheme() {
    saveTheme(!_isDarkMode.value);
    Get.changeThemeMode(_isDarkMode.value ? ThemeMode.dark : ThemeMode.light);
  }
}

var colorScheme = ColorScheme.fromSeed(
  seedColor: Color.fromARGB(255, 56, 124, 228),
);

final ThemeData themeData = ThemeData(
  // primarySwatch: Colors.blue,
  colorScheme: colorScheme,
  useMaterial3: true,
);
var textButton = TextButtonThemeData(
  style: TextButton.styleFrom(foregroundColor: colorScheme.background),
);

final themeLight = ThemeData.light(useMaterial3: true);
final themeDark = ThemeData.dark(useMaterial3: true);
