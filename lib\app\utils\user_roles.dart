import 'package:get/get.dart';
import 'package:ibcb_desktop/app/data/services/auth/auth_service.dart';

class UserRolesUtils {
  static bool isAdminOrModerator() {
    final AuthService authUser = Get.find<AuthService>();
    final user = authUser.user.value;

    if (user != null) {
      return user.hasRole(1) || user.hasRole(2);
    }

    return false;
  }

  static bool isMember() {
    final AuthService authUser = Get.find<AuthService>();
    final user = authUser.user.value;

    if (user != null) {
      return user.hasRole(1) || user.hasRole(2) || user.hasRole(3);
    }

    return false;
  }
}
