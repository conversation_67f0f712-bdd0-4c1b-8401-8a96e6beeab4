class FinanceModel {
  final String date;
  final double caixaBalance;
  final double missoesBalance;
  final double cecabiBalance;
  final double terrenoBalance;
  final double cdbBalance;

  // Movimentos do período
  final double caixaEntradas;
  final double caixaSaidas;
  final double missoesEntradas;
  final double missoesSaidas;
  final double cecabiEntradas;
  final double cecabiSaidas;
  final double terrenoEntradas;
  final double terrenoSaidas;

  // Saldos mensais
  double get caixaMensal => caixaEntradas - caixaSaidas;
  double get missoesMensal => missoesEntradas - missoesSaidas;
  double get cecabiMensal => cecabiEntradas - cecabiSaidas;
  double get terrenoMensal => terrenoEntradas - terrenoSaidas;

  FinanceModel({
    required this.date,
    required this.caixaBalance,
    required this.missoesBalance,
    required this.cecabiBalance,
    required this.terrenoBalance,
    required this.cdbBalance,
    required this.caixaEntradas,
    required this.caixaSaidas,
    required this.missoesEntradas,
    required this.missoesSaidas,
    required this.cecabiEntradas,
    required this.cecabiSaidas,
    required this.terrenoEntradas,
    required this.terrenoSaidas,
  });

  factory FinanceModel.fromMap(Map<String, dynamic> map) {
    return FinanceModel(
      date: map['date'],
      caixaBalance: map['caixaBalance'],
      missoesBalance: map['missoesBalance'],
      cecabiBalance: map['cecabiBalance'],
      terrenoBalance: map['terrenoBalance'],
      cdbBalance: map['cdbBalance'],
      caixaEntradas: map['caixaEntradas'],
      caixaSaidas: map['caixaSaidas'],
      missoesEntradas: map['missoesEntradas'],
      missoesSaidas: map['missoesSaidas'],
      cecabiEntradas: map['cecabiEntradas'],
      cecabiSaidas: map['cecabiSaidas'],
      terrenoEntradas: map['terrenoEntradas'],
      terrenoSaidas: map['terrenoSaidas'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'date': date,
      'caixaBalance': caixaBalance,
      'missoesBalance': missoesBalance,
      'cecabiBalance': cecabiBalance,
      'terrenoBalance': terrenoBalance,
      'cdbBalance': cdbBalance,
      'caixaEntradas': caixaEntradas,
      'caixaSaidas': caixaSaidas,
      'missoesEntradas': missoesEntradas,
      'missoesSaidas': missoesSaidas,
      'cecabiEntradas': cecabiEntradas,
      'cecabiSaidas': cecabiSaidas,
      'terrenoEntradas': terrenoEntradas,
      'terrenoSaidas': terrenoSaidas,
    };
  }
}

class FinanceEntryModel {
  final String date;
  final double dizimoDinheiro;
  final double dizimoPix;
  final double ofertasDinheiro;
  final double ofertasPix;
  final double missoes;
  final double cecabi;
  final double cdb;
  final double terreno;

  double get total =>
      dizimoDinheiro +
      dizimoPix +
      ofertasDinheiro +
      ofertasPix +
      missoes +
      cecabi +
      cdb +
      terreno;

  FinanceEntryModel({
    required this.date,
    required this.dizimoDinheiro,
    required this.dizimoPix,
    required this.ofertasDinheiro,
    required this.ofertasPix,
    required this.missoes,
    required this.cecabi,
    required this.cdb,
    required this.terreno,
  });

  factory FinanceEntryModel.fromMap(Map<String, dynamic> map) {
    return FinanceEntryModel(
      date: map['date'],
      dizimoDinheiro: map['dizimoDinheiro'],
      dizimoPix: map['dizimoPix'],
      ofertasDinheiro: map['ofertasDinheiro'],
      ofertasPix: map['ofertasPix'],
      missoes: map['missoes'],
      cecabi: map['cecabi'],
      cdb: map['cdb'],
      terreno: map['terreno'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'date': date,
      'dizimoDinheiro': dizimoDinheiro,
      'dizimoPix': dizimoPix,
      'ofertasDinheiro': ofertasDinheiro,
      'ofertasPix': ofertasPix,
      'missoes': missoes,
      'cecabi': cecabi,
      'cdb': cdb,
      'terreno': terreno,
    };
  }
}

class FinanceExitModel {
  final String date;
  final double ajudaCustoPastoral;
  final double aluguel;
  final double energiaEletrica;
  final double telefone;
  final double semae;
  final double cbn;
  final double manutencao;
  final double tarifasBancarias;
  final double missoes;
  final double cecabi;
  final double terreno;

  double get total =>
      ajudaCustoPastoral +
      aluguel +
      energiaEletrica +
      telefone +
      semae +
      cbn +
      manutencao +
      tarifasBancarias +
      missoes +
      cecabi +
      terreno;

  FinanceExitModel({
    required this.date,
    required this.ajudaCustoPastoral,
    required this.aluguel,
    required this.energiaEletrica,
    required this.telefone,
    required this.semae,
    required this.cbn,
    required this.manutencao,
    required this.tarifasBancarias,
    required this.missoes,
    required this.cecabi,
    required this.terreno,
  });

  factory FinanceExitModel.fromMap(Map<String, dynamic> map) {
    return FinanceExitModel(
      date: map['date'],
      ajudaCustoPastoral: map['ajudaCustoPastoral'],
      aluguel: map['aluguel'],
      energiaEletrica: map['energiaEletrica'],
      telefone: map['telefone'],
      semae: map['semae'],
      cbn: map['cbn'],
      manutencao: map['manutencao'],
      tarifasBancarias: map['tarifasBancarias'],
      missoes: map['missoes'],
      cecabi: map['cecabi'],
      terreno: map['terreno'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'date': date,
      'ajudaCustoPastoral': ajudaCustoPastoral,
      'aluguel': aluguel,
      'energiaEletrica': energiaEletrica,
      'telefone': telefone,
      'semae': semae,
      'cbn': cbn,
      'manutencao': manutencao,
      'tarifasBancarias': tarifasBancarias,
      'missoes': missoes,
      'cecabi': cecabi,
      'terreno': terreno,
    };
  }
}

class FinanceCategoryEntry {
  final String id;
  final String category;
  final double value;
  final String date;
  final String description;

  FinanceCategoryEntry({
    required this.id,
    required this.category,
    required this.value,
    required this.date,
    this.description = '',
  });

  factory FinanceCategoryEntry.fromMap(Map<String, dynamic> map, String docId) {
    return FinanceCategoryEntry(
      id: docId,
      category: map['category'] ?? '',
      value: (map['value'] ?? 0.0).toDouble(),
      date: map['date'] ?? '',
      description: map['description'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'category': category,
      'value': value,
      'date': date,
      'description': description,
    };
  }
}

class FinanceCategoryExit {
  final String id;
  final String category;
  final double value;
  final String date;
  final String description;

  FinanceCategoryExit({
    required this.id,
    required this.category,
    required this.value,
    required this.date,
    this.description = '',
  });

  factory FinanceCategoryExit.fromMap(Map<String, dynamic> map, String docId) {
    return FinanceCategoryExit(
      id: docId,
      category: map['category'] ?? '',
      value: (map['value'] ?? 0.0).toDouble(),
      date: map['date'] ?? '',
      description: map['description'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'category': category,
      'value': value,
      'date': date,
      'description': description,
    };
  }
}

class MonthlyFinanceModel {
  final String id; // formato: MM-YYYY
  final List<FinanceCategoryEntry> entries;
  final List<FinanceCategoryExit> exits;
  final Map<String, double> balances;

  MonthlyFinanceModel({
    required this.id,
    required this.entries,
    required this.exits,
    required this.balances,
  });

  double get totalEntries =>
      entries.fold(0.0, (sum, entry) => sum + entry.value);

  double get totalExits => exits.fold(0.0, (sum, exit) => sum + exit.value);

  double get monthlyBalance => totalEntries - totalExits;

  // Agrupa entradas por categoria
  Map<String, double> get entriesByCategory {
    final Map<String, double> result = {};
    for (var entry in entries) {
      result[entry.category] = (result[entry.category] ?? 0) + entry.value;
    }
    return result;
  }

  // Agrupa saídas por categoria
  Map<String, double> get exitsByCategory {
    final Map<String, double> result = {};
    for (var exit in exits) {
      result[exit.category] = (result[exit.category] ?? 0) + exit.value;
    }
    return result;
  }
}
