import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/providers/user/user_api_provider.dart';
import '../../../data/models/user_model.dart';
import '../../../data/models/role.dart';
import '../../../core/logger/app_logger.dart';
import '../../../helpers/snack_bar_msg.dart';
import '../../../routes/pages.dart';

class UsersController extends GetxController {
  static final _logger = AppLogger.logger;

  final UserApiProvider _userApi = Get.find<UserApiProvider>();
  final SnackBarMsg _snackBarMsg = SnackBarMsg();

  final users = <UserModel>[].obs;
  final roles = <Role>[].obs;
  final isLoading = false.obs;
  final searchQuery = ''.obs;

  final searchController = TextEditingController();

  // Pagination
  final currentPage = 1.obs;
  final itemsPerPage = 20;
  final hasMoreData = true.obs;

  // Filters
  final selectedRole = Rxn<Role>();

  List<UserModel> get filteredUsers {
    var filtered =
        users.where((user) {
          final matchesSearch =
              searchQuery.value.isEmpty ||
              user.nome?.toLowerCase().contains(
                    searchQuery.value.toLowerCase(),
                  ) ==
                  true ||
              user.email.toLowerCase().contains(
                searchQuery.value.toLowerCase(),
              );

          final matchesRole =
              selectedRole.value == null ||
              user.cargos?.any((role) => role.id == selectedRole.value!.id) ==
                  true;

          return matchesSearch && matchesRole;
        }).toList();

    return filtered;
  }

  @override
  void onInit() {
    super.onInit();
    _logger.debug('UsersController initialized');
    loadUsers();
    loadRoles();

    // Setup search debounce
    searchController.addListener(() {
      searchQuery.value = searchController.text;
    });
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }

  Future<void> loadUsers() async {
    try {
      isLoading.value = true;

      final response = await _userApi.getUsers(
        page: currentPage.value,
        perPage: itemsPerPage,
        search: searchQuery.value.isEmpty ? null : searchQuery.value,
      );

      if (response['users'] != null) {
        final usersList =
            (response['users'] as List)
                .map((user) => UserModel.fromMap(user))
                .toList();

        if (currentPage.value == 1) {
          users.value = usersList;
        } else {
          users.addAll(usersList);
        }

        hasMoreData.value = usersList.length == itemsPerPage;
        _logger.info('Loaded ${usersList.length} users');
      }
    } catch (e) {
      _logger.error('Failed to load users', e);
      _snackBarMsg.showSnack(
        'Erro',
        'Falha ao carregar usuários: ${e.toString()}',
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> loadRoles() async {
    try {
      final rolesList = await _userApi.fetchCargos();
      roles.value = rolesList;

      _logger.info('Loaded ${rolesList.length} roles');
    } catch (e) {
      _logger.error('Failed to load roles', e);
      _snackBarMsg.showSnack(
        'Erro',
        'Falha ao carregar cargos: ${e.toString()}',
      );
    }
  }

  Future<void> refreshUsers() async {
    await loadUsers();
  }

  void searchUsers(String query) {
    searchQuery.value = query;
  }

  void filterByRole(Role? role) {
    selectedRole.value = role;
  }

  void clearFilters() {
    searchController.clear();
    searchQuery.value = '';
    selectedRole.value = null;
  }

  Future<void> updateUserRoles(UserModel user, List<int> roleIds) async {
    try {
      isLoading.value = true;

      await _userApi.updateUserCargos(user.id!, roleIds);

      // Update local user data
      final userIndex = users.indexWhere((u) => u.id == user.id);
      if (userIndex != -1) {
        final updatedRoles =
            roles.where((role) => roleIds.contains(role.id)).toList();
        users[userIndex] = UserModel(
          id: user.id,
          email: user.email,
          nome: user.nome,
          imagemPerfil: user.imagemPerfil,
          niver: user.niver,
          criado: user.criado,
          tipo_conta: user.tipo_conta,
          cargos: updatedRoles,
        );
      }

      _snackBarMsg.showSnack(
        'Sucesso',
        'Cargos do usuário atualizados com sucesso',
      );
      _logger.info('Updated roles for user ${user.email}');
    } catch (e) {
      _logger.error('Failed to update user roles', e);
      _snackBarMsg.showSnack(
        'Erro',
        'Falha ao atualizar cargos: ${e.toString()}',
      );
    } finally {
      isLoading.value = false;
    }
  }

  void goToCreateUser() {
    Get.toNamed(Routes.userCreate);
  }

  void goToEditUser(UserModel user) {
    Get.toNamed(Routes.userEdit, arguments: user);
  }

  void goToUserRoles(UserModel user) {
    Get.toNamed(Routes.userRoles, arguments: user);
  }

  Future<void> confirmDeleteUser(UserModel user) async {
    final result = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Confirmar Exclusão'),
        content: Text('Tem certeza que deseja excluir o usuário ${user.nome}?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Get.back(result: true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Excluir'),
          ),
        ],
      ),
    );

    if (result == true) {
      await deleteUser(user);
    }
  }

  Future<void> deleteUser(UserModel user) async {
    try {
      isLoading.value = true;

      // Note: This would need to be implemented in the API
      // await _userApi.deleteUser(user.id!);

      users.removeWhere((u) => u.id == user.id);

      _snackBarMsg.showSnack('Sucesso', 'Usuário excluído com sucesso');
      _logger.info('Deleted user ${user.email}');
    } catch (e) {
      _logger.error('Failed to delete user', e);
      _snackBarMsg.showSnack(
        'Erro',
        'Falha ao excluir usuário: ${e.toString()}',
      );
    } finally {
      isLoading.value = false;
    }
  }
}
