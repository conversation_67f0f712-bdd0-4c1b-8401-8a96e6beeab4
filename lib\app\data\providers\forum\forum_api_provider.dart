import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:ibcb_desktop/app/data/models/forum_model.dart';
import 'package:ibcb_desktop/app/data/providers/base/base_api_provider.dart';

/// Provedor de API para operações relacionadas ao fórum
/// Inclui tópicos do fórum e comentários aninhados
class ForumApiProvider extends BaseApiProvider {
  // ================================================================
  // TÓPICOS DO FÓRUM
  // ================================================================

  Future<Map<String, dynamic>> fetchForumTopics({
    int page = 1,
    int perPage = 5,
  }) async {
    try {
      Response response = await dio.get(
        'forum',
        queryParameters: {'page': page, 'perPage': perPage},
      );
      return response.data;
    } on DioException catch (e) {
      debugPrint(e.message);
      throw handleDioException(e);
    }
  }

  Future<void> createForumTopic(FormData data) async {
    try {
      Response response = await dio.post('forum', data: data);
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<void> updateForumTopic(int id, Map<String, dynamic> data) async {
    try {
      await dio.put('forum/$id', data: data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<void> deleteForumTopic(int id) async {
    try {
      await dio.delete('forum/$id');
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<ForumModel> fetchForumTopicById(int topicId) async {
    try {
      Response response = await dio.get('forum/$topicId');
      return ForumModel.fromJson(response.data);
    } catch (e) {
      debugPrint('Error fetching forum topic by id: $e');
      rethrow;
    }
  }

  Future<void> changeForumStatus(int topicId, bool fechado) async {
    try {
      debugPrint('Changing forum status: topicId=$topicId, fechado=$fechado');
      final response = await dio.put(
        '/forum/$topicId/status',
        data: {'fechado': fechado},
      );
      debugPrint('Response: ${response.data}');
    } on DioException catch (e) {
      debugPrint('Error: ${e.response?.data}');
      throw handleDioException(e);
    }
  }

  // ================================================================
  // COMENTÁRIOS DO FÓRUM
  // ================================================================

  /// Buscar comentários de um tópico do fórum com estrutura aninhada
  Future<List<Map<String, dynamic>>> getForumComments(int topicId) async {
    try {
      Response response = await dio.get('forum/$topicId/comment');
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  /// Buscar árvore completa de comentários de um tópico
  Future<List<Map<String, dynamic>>> getForumCommentsTree(int topicId) async {
    try {
      Response response = await dio.get('forum/$topicId/comments/tree');
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  /// Criar comentário ou resposta no fórum
  Future<Map<String, dynamic>> createForumComment(
    int topicId,
    String content, {
    int? parentId,
  }) async {
    try {
      Map<String, dynamic> data = {'conteudo': content};
      if (parentId != null) {
        data['parent_id'] = parentId;
      }

      Response response = await dio.post('forum/$topicId/comment', data: data);
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  /// Responder a um comentário específico
  Future<Map<String, dynamic>> replyToForumComment(
    int commentId,
    String content,
  ) async {
    try {
      Response response = await dio.post(
        'comment/$commentId/reply',
        data: {'conteudo': content},
      );
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  /// Deletar um comentário do fórum
  Future<void> deleteForumComment(int commentId) async {
    try {
      await dio.delete('comment/$commentId');
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }
}
