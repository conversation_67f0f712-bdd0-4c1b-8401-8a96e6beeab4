import 'package:get/get.dart';
import '../../data/services/auth/auth_service.dart';
import '../../data/services/auth/auth_repository.dart';
import '../../data/services/storage/storage_service.dart';
import '../../data/providers/api.dart';
import '../../data/providers/user/user_api_provider.dart';
import '../../data/providers/post/post_api_provider.dart';
import '../theme/app_theme.dart';
import '../logger/app_logger.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    AppLogger.logger.debug('Initializing global dependencies...');

    // Core Services
    Get.put(StorageService(), permanent: true);
    Get.put(Api(), permanent: true);

    // API Providers
    Get.put(UserApiProvider(), permanent: true);
    Get.put(PostApiProvider(), permanent: true);

    // Theme Service
    Get.put(ThemeService(), permanent: true);

    // Auth Services
    Get.put(AuthRepository(Get.find<Api>()), permanent: true);
    Get.put(AuthService(Get.find<AuthRepository>()), permanent: true);

    AppLogger.logger.info('Global dependencies initialized successfully');
  }
}
