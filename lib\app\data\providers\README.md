# API Providers - Arquitetura Modular

Esta pasta contém a nova arquitetura modular dos provedores de API, organizados por domínio funcional.

## Estrutura

```
providers/
├── base/
│   └── base_api_provider.dart          # Classe base com configuração comum do Dio
├── user/
│   └── user_api_provider.dart          # APIs de usuários, autenticação e roles
├── forum/
│   └── forum_api_provider.dart         # APIs do fórum e comentários
├── post/
│   └── post_api_provider.dart          # APIs de posts e comentários
├── event/
│   └── event_api_provider.dart         # APIs de eventos simples
├── calendar/
│   └── calendar_api_provider.dart      # APIs do calendário de eventos
├── providers.dart                      # Exporta todos os providers
├── api_v2.dart                        # API principal modular
└── api.dart                           # API legada (será removida gradualmente)
```

## Características

### BaseApiProvider
- Configuração centralizada do Dio
- Interceptors para autenticação, logging e tratamento de erros
- Métodos utilitários para retry automático
- Tratamento padronizado de exceções

### Providers Específicos
Cada provider herda de `BaseApiProvider` e contém apenas métodos relacionados ao seu domínio:

- **UserApiProvider**: Autenticação, perfis, gerenciamento de usuários e roles
- **ForumApiProvider**: Tópicos do fórum e sistema de comentários aninhados
- **PostApiProvider**: Posts e comentários de posts
- **EventApiProvider**: Eventos simples
- **CalendarApiProvider**: Calendário com recursos avançados (participação, categorias, etc.)

## Uso

### Forma Recomendada (Modular)
```dart
// Injetar providers específicos
Get.put<UserApiProvider>(UserApiProvider());

// Usar diretamente
final userApi = Get.find<UserApiProvider>();
final user = await userApi.getUser();

// Ou através da API principal
final api = Get.find<Api>();
final events = await api.calendar.getCalendarEvents();
```

### Forma de Compatibilidade (Transição)
```dart
// Ainda funciona como antes para facilitar migração
final api = Get.find<Api>();
final user = await api.getUser();
```

## Benefícios

1. **Organização**: Código separado por domínio funcional
2. **Manutenibilidade**: Mais fácil encontrar e modificar funcionalidades específicas
3. **Testabilidade**: Providers podem ser testados independentemente
4. **Reutilização**: Configuração comum centralizada no BaseApiProvider
5. **Escalabilidade**: Fácil adicionar novos domínios sem afetar existentes

## Migração

A classe `Api` mantém compatibilidade total com o código existente, delegando chamadas para os providers apropriados. Isso permite migração gradual do código existente.

### Para Controllers Novos
Use diretamente os providers específicos:
```dart
class UserController extends GetxController {
  final UserApiProvider _userApi = Get.find<UserApiProvider>();
  
  Future<void> login() async {
    final result = await _userApi.login(loginData);
    // ...
  }
}
```

### Para Controllers Existentes
Mantenha o uso atual da classe `Api` que continuará funcionando normalmente.
