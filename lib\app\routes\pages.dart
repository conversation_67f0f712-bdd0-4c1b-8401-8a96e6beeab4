import 'package:get/get.dart';

// Controllers
import '../modules/auth/controllers/login_controller.dart';
import '../modules/dashboard/controllers/dashboard_controller.dart';
import '../modules/users/controllers/users_controller.dart';
import '../modules/forum/controllers/forum_controller.dart';
import '../modules/posts/controllers/posts_controller.dart';
import '../modules/events/controllers/events_controller.dart';
import '../modules/calendar/controllers/calendar_controller.dart';
import '../modules/formation/controllers/formation_controller.dart';
import '../modules/finance/controllers/finance_controller.dart';

// Views
import '../modules/splash/views/splash_view.dart';
import '../modules/auth/views/login_view.dart';
import '../modules/dashboard/views/dashboard_view.dart';
import '../modules/users/views/users_view.dart';
import '../modules/users/views/user_create_view.dart';
import '../modules/users/views/user_edit_view.dart';
import '../modules/users/views/user_roles_view.dart';
import '../modules/forum/views/forum_view.dart';
import '../modules/forum/views/forum_create_view.dart';
import '../modules/forum/views/forum_edit_view.dart';
import '../modules/forum/views/forum_detail_view.dart';
import '../modules/posts/views/posts_view.dart';
import '../modules/posts/views/post_create_view.dart';
import '../modules/posts/views/post_edit_view.dart';
import '../modules/posts/views/post_detail_view.dart';
import '../modules/events/views/events_view.dart';
import '../modules/events/views/event_create_view.dart';
import '../modules/events/views/event_edit_view.dart';
import '../modules/events/views/event_detail_view.dart';
import '../modules/calendar/views/calendar_view.dart';
import '../modules/calendar/views/calendar_create_view.dart';
import '../modules/calendar/views/calendar_edit_view.dart';
import '../modules/formation/views/formation_view.dart';
import '../modules/formation/views/formation_types_view.dart';
import '../modules/formation/views/formation_studies_view.dart';
import '../modules/formation/views/formation_quizzes_view.dart';
import '../modules/finance/views/finance_view.dart';
import '../modules/finance/views/finance_create_view.dart';
import '../modules/finance/views/finance_edit_view.dart';

// Bindings
import '../modules/splash/bindings/splash_binding.dart';
import '../modules/auth/bindings/login_binding.dart';
import '../modules/dashboard/bindings/dashboard_binding.dart';
import '../modules/users/bindings/users_binding.dart';
import '../modules/forum/bindings/forum_binding.dart';
import '../modules/posts/bindings/posts_binding.dart';
import '../modules/events/bindings/events_binding.dart';
import '../modules/calendar/bindings/calendar_binding.dart';
import '../modules/formation/bindings/formation_binding.dart';
import '../modules/finance/bindings/finance_binding.dart';

part 'routes.dart';

abstract class AppPages {
  static final pages = [
    // Splash
    GetPage(
      name: Routes.splashScreen,
      page: () => const SplashView(),
      binding: SplashBinding(),
    ),

    // Auth
    GetPage(
      name: Routes.login,
      page: () => const LoginView(),
      binding: LoginBinding(),
    ),

    // Dashboard
    GetPage(
      name: Routes.dashboard,
      page: () => const DashboardView(),
      binding: DashboardBinding(),
    ),

    // User Management
    GetPage(
      name: Routes.users,
      page: () => const UsersView(),
      binding: UsersBinding(),
    ),
    GetPage(
      name: Routes.userCreate,
      page: () => const UserCreateView(),
      binding: UsersBinding(),
    ),
    GetPage(
      name: Routes.userEdit,
      page: () => const UserEditView(),
      binding: UsersBinding(),
    ),
    GetPage(
      name: Routes.userRoles,
      page: () => const UserRolesView(),
      binding: UsersBinding(),
    ),

    // Forum Management
    GetPage(
      name: Routes.forum,
      page: () => const ForumView(),
      binding: ForumBinding(),
    ),
    GetPage(
      name: Routes.forumCreate,
      page: () => const ForumCreateView(),
      binding: ForumBinding(),
    ),
    GetPage(
      name: Routes.forumEdit,
      page: () => const ForumEditView(),
      binding: ForumBinding(),
    ),
    GetPage(
      name: Routes.forumView,
      page: () => const ForumDetailView(),
      binding: ForumBinding(),
    ),

    // Posts Management
    GetPage(
      name: Routes.posts,
      page: () => const PostsView(),
      binding: PostsBinding(),
    ),
    GetPage(
      name: Routes.postCreate,
      page: () => const PostCreateView(),
      binding: PostsBinding(),
    ),
    GetPage(
      name: Routes.postEdit,
      page: () => const PostEditView(),
      binding: PostsBinding(),
    ),
    GetPage(
      name: Routes.postView,
      page: () => const PostDetailView(),
      binding: PostsBinding(),
    ),

    // Events Management
    GetPage(
      name: Routes.events,
      page: () => const EventsView(),
      binding: EventsBinding(),
    ),
    GetPage(
      name: Routes.eventCreate,
      page: () => const EventCreateView(),
      binding: EventsBinding(),
    ),
    GetPage(
      name: Routes.eventEdit,
      page: () => const EventEditView(),
      binding: EventsBinding(),
    ),
    GetPage(
      name: Routes.eventView,
      page: () => const EventDetailView(),
      binding: EventsBinding(),
    ),

    // Calendar Management
    GetPage(
      name: Routes.calendar,
      page: () => const CalendarView(),
      binding: CalendarBinding(),
    ),
    GetPage(
      name: Routes.calendarCreate,
      page: () => const CalendarCreateView(),
      binding: CalendarBinding(),
    ),
    GetPage(
      name: Routes.calendarEdit,
      page: () => const CalendarEditView(),
      binding: CalendarBinding(),
    ),

    // Formation Management
    GetPage(
      name: Routes.formation,
      page: () => const FormationView(),
      binding: FormationBinding(),
    ),
    GetPage(
      name: Routes.formationType,
      page: () => const FormationTypesView(),
      binding: FormationBinding(),
    ),
    GetPage(
      name: Routes.formationStudy,
      page: () => const FormationStudiesView(),
      binding: FormationBinding(),
    ),
    GetPage(
      name: Routes.formationQuiz,
      page: () => const FormationQuizzesView(),
      binding: FormationBinding(),
    ),

    // Finance Management
    GetPage(
      name: Routes.finance,
      page: () => const FinanceView(),
      binding: FinanceBinding(),
    ),
    GetPage(
      name: Routes.financeCreate,
      page: () => const FinanceCreateView(),
      binding: FinanceBinding(),
    ),
    GetPage(
      name: Routes.financeEdit,
      page: () => const FinanceEditView(),
      binding: FinanceBinding(),
    ),
  ];
}
