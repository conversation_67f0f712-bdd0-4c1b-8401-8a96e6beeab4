
import 'package:ibcb_desktop/app/data/models/role.dart';
import 'package:ibcb_desktop/app/data/providers/api.dart';

class UserRepository {
  final Api _api;

  UserRepository(this._api);

  fetchUsers() {
    return _api.fetchUsers();
  }

  Future<void> updateUserCargos(int userId, List<int> cargoIds) {
    return _api.updateUserCargos(userId, cargoIds);
  }

  Future<List<Role>> fetchCargos() {
    return _api.fetchCargos();
  }
}
