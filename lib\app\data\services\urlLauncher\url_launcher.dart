import 'package:url_launcher/url_launcher.dart';

class UrlLauncher {
  final String _instaIbcb = 'https://www.instagram.com/ibcbpira/';
  final String _instaCecabi = 'https://www.instagram.com/projetocecabi/';
  final String _youtubeIbcb =
      'https://www.youtube.com/@igrejabatistacasabiblica';
  final String _spotify =
      'https://open.spotify.com/intl-pt/artist/224ZYCldiwS97uQrCFzLgs?si=j_8_SgTtSYiBCz-E56w9xA&nd=1&dlsi=824265bf24b240d6';

  String get instaIbcb => _instaIbcb;
  String get instaCecabi => _instaCecabi;
  String get youtubeIbcb => _youtubeIbcb;
  String get spotify => _spotify;

  Future<void> launchLink(String urlSite) async {
    final Uri url = Uri.parse(urlSite);

    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.inAppBrowserView);
    } else {
      throw 'Could not launch $urlSite';
    }
  }

  Future launchLinkFirebase(String urlSite) async {
    if (await launchUrl(
      Uri.parse(urlSite),
      mode: LaunchMode.externalNonBrowserApplication,
    )) {} //throw 'Could not launch $url';
  }
}
