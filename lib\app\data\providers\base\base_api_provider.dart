import 'package:dio/dio.dart';
import 'package:get/instance_manager.dart';
import 'package:ibcb_desktop/app/core/logger/app_logger.dart';
import 'package:ibcb_desktop/app/data/providers/api_exception.dart';
import 'package:ibcb_desktop/app/data/services/storage/storage_service.dart';

/// Classe base para todos os provedores de API
/// Contém a configuração comum do Dio e métodos utilitários
abstract class BaseApiProvider {
  late final Dio _dio;
  final StorageService _storageService = Get.find<StorageService>();
  static final _logger = ModuleLogger.create('API');

  BaseApiProvider() {
    _dio = Dio(
      BaseOptions(
        baseUrl: 'http://************:3333/',
        // baseUrl: 'https://realdev.top/',
        connectTimeout: const Duration(seconds: 60),
        receiveTimeout: const Duration(seconds: 120),
        sendTimeout: const Duration(seconds: 60),
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      ),
    );

    _setupInterceptors();
  }

  /// Getter protegido para acessar o Dio nas classes filhas
  Dio get dio => _dio;

  void _setupInterceptors() {
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          String? token = _storageService.token;
          if (token != null) {
            options.headers['Authorization'] = 'Bearer $token';
          }

          // Handle different data types for logging
          Map<String, dynamic>? logData;
          if (options.data is Map<String, dynamic>) {
            logData = options.data as Map<String, dynamic>;
          } else if (options.data is FormData) {
            // For FormData, create a summary map for logging
            final formData = options.data as FormData;
            logData = {
              'fields': Map.fromEntries(formData.fields),
              'files':
                  formData.files
                      .map(
                        (file) => {
                          'key': file.key,
                          'value':
                              'MultipartFile(${file.value.filename ?? 'unknown'})',
                        },
                      )
                      .toList(),
            };
          }
          _logger.apiRequest(options.method, options.path, logData);
          return handler.next(options);
        },
        onResponse: (response, handler) async {
          _logger.apiResponse(
            response.requestOptions.method,
            response.requestOptions.path,
            response.statusCode ?? 0,
            response.data,
          );
          return handler.next(response);
        },
        onError: (DioException e, handler) async {
          _logger.error('API Error: $e', e);
          if (e.response != null) {
            _logger.error('API Error Response Data: \\n${e.response?.data}', e);
          }

          // Tratar erros de timeout especificamente
          if (e.type == DioExceptionType.connectionTimeout) {
            handler.reject(
              DioException(
                requestOptions: e.requestOptions,
                type: DioExceptionType.connectionTimeout,
                error: NetworkException(
                  'Tempo limite de conexão excedido. Verifique sua conexão com a internet e tente novamente.',
                ),
              ),
            );
            return;
          }

          if (e.type == DioExceptionType.receiveTimeout) {
            handler.reject(
              DioException(
                requestOptions: e.requestOptions,
                type: DioExceptionType.receiveTimeout,
                error: NetworkException(
                  'Tempo limite de recepção excedido. O servidor está demorando muito para responder.',
                ),
              ),
            );
            return;
          }

          if (e.type == DioExceptionType.sendTimeout) {
            handler.reject(
              DioException(
                requestOptions: e.requestOptions,
                type: DioExceptionType.sendTimeout,
                error: NetworkException(
                  'Tempo limite de envio excedido. Verifique sua conexão com a internet.',
                ),
              ),
            );
            return;
          }

          if (e.type == DioExceptionType.connectionError) {
            handler.reject(
              DioException(
                requestOptions: e.requestOptions,
                type: DioExceptionType.connectionError,
                error: NetworkException(
                  'Erro de conexão. Verifique se você está conectado à internet e se o servidor está disponível.',
                ),
              ),
            );
            return;
          }

          if (e.type == DioExceptionType.unknown) {
            handler.reject(
              DioException(
                requestOptions: e.requestOptions,
                type: DioExceptionType.unknown,
                error: NetworkException(
                  'Erro de rede desconhecido. Verifique sua conexão com a internet.',
                ),
              ),
            );
            return;
          }

          if (e.response != null) {
            String? backendMessage;
            if (e.response?.data is Map &&
                e.response?.data['message'] != null) {
              backendMessage = e.response?.data['message'].toString();
            } else if (e.response?.data is String) {
              backendMessage = e.response?.data;
            }
            switch (e.response!.statusCode) {
              case 400:
                handler.reject(
                  DioException(
                    requestOptions: e.requestOptions,
                    response: e.response,
                    type: DioExceptionType.badResponse,
                    error: BadRequestException(
                      backendMessage ??
                          'Requisição inválida: ${e.response!.data}',
                    ),
                  ),
                );
                break;
              case 401:
                if (e.response!.data['message'] == 'Invalid credentials') {
                  handler.reject(
                    DioException(
                      requestOptions: e.requestOptions,
                      response: e.response,
                      type: DioExceptionType.badResponse,
                      error: InvalidCredentialsException(
                        backendMessage ??
                            'Credenciais inválidas: ${e.response!.data}',
                      ),
                    ),
                  );
                } else {
                  handler.reject(
                    DioException(
                      requestOptions: e.requestOptions,
                      response: e.response,
                      type: DioExceptionType.badResponse,
                      error: UnauthorizedException(
                        backendMessage ?? 'Não autorizado: ${e.response!.data}',
                      ),
                    ),
                  );
                }
                break;
              case 404:
                handler.reject(
                  DioException(
                    requestOptions: e.requestOptions,
                    response: e.response,
                    type: DioExceptionType.badResponse,
                    error: NotFoundException(
                      backendMessage ?? 'Não encontrado: ${e.response!.data}',
                    ),
                  ),
                );
                break;
              case 500:
                handler.reject(
                  DioException(
                    requestOptions: e.requestOptions,
                    response: e.response,
                    type: DioExceptionType.badResponse,
                    error: InternalServerException(
                      backendMessage ??
                          'Erro interno do servidor: ${e.response!.data}',
                    ),
                  ),
                );
                break;
              default:
                handler.reject(
                  DioException(
                    requestOptions: e.requestOptions,
                    response: e.response,
                    type: DioExceptionType.badResponse,
                    error: UnknownException(
                      backendMessage ??
                          'Erro desconhecido: ${e.response!.data}',
                    ),
                  ),
                );
            }
          } else {
            handler.reject(
              DioException(
                requestOptions: e.requestOptions,
                type: DioExceptionType.badResponse,
                error: NetworkException('Erro de rede: ${e.message}'),
              ),
            );
          }
        },
      ),
    );
  }

  /// Método para lidar com exceções do Dio
  Exception handleDioException(DioException e) {
    if (e.error is ApiException) {
      return e.error as ApiException;
    } else {
      return UnknownException('Erro desconhecido: ${e.message}');
    }
  }

  /// Método para executar requisições com retry automático em caso de timeout
  Future<T> executeWithRetry<T>(
    Future<T> Function() request, {
    int maxRetries = 3,
  }) async {
    int attempt = 0;
    while (attempt <= maxRetries) {
      try {
        return await request();
      } on DioException catch (e) {
        attempt++;

        // Se for um erro de timeout, erro de rede, ou erro de conexão e ainda não atingiu o máximo de tentativas
        if ((e.type == DioExceptionType.connectionTimeout ||
                e.type == DioExceptionType.receiveTimeout ||
                e.type == DioExceptionType.sendTimeout ||
                e.type == DioExceptionType.connectionError ||
                e.type == DioExceptionType.unknown) &&
            attempt <= maxRetries) {
          _logger.warning(
            'Tentativa $attempt/${maxRetries + 1} falhou: ${e.type}. Tentando novamente em ${attempt * 3} segundos...',
          );

          // Aguarda um tempo crescente antes de tentar novamente (3s, 6s, 9s)
          await Future.delayed(Duration(seconds: attempt * 3));
          continue;
        }

        // Se não for um erro que permite retry ou já atingiu o máximo de tentativas, relança o erro
        rethrow;
      }
    }

    // Nunca deveria chegar aqui, mas por segurança
    throw UnknownException('Falha após todas as tentativas');
  }
}
