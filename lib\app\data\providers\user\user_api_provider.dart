import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:ibcb_desktop/app/core/logger/app_logger.dart';
import 'package:ibcb_desktop/app/data/models/user_login_req_model.dart';
import 'package:ibcb_desktop/app/data/models/user_login_res_model.dart';
import 'package:ibcb_desktop/app/data/models/user_model.dart';
import 'package:ibcb_desktop/app/data/models/user_profile_model.dart';
import 'package:ibcb_desktop/app/data/models/role.dart';
import 'package:ibcb_desktop/app/data/providers/base/base_api_provider.dart';

/// Provedor de API para operações relacionadas a usuários
/// Inclui autenticação, perfis e gerenciamento de usuários
class UserApiProvider extends BaseApiProvider {
  // ================================================================
  // AUTENTICAÇÃO
  // ================================================================

  Future<UserModel> getUser() async {
    return executeWithRetry(() async {
      try {
        Response response = await dio.get('auth');
        AppLogger.logger.debug('getUser response data: ${response.data}');
        return UserModel.fromMap(response.data['user']);
      } on DioException catch (e) {
        throw handleDioException(e);
      }
    });
  }

  Future<UserLoginResponseModel> login(UserLoginReqModel data) async {
    return executeWithRetry(() async {
      try {
        Response response = await dio.post('login', data: data.toMap());
        AppLogger.logger.debug('login response data: ${response.data}');
        return UserLoginResponseModel.fromMap(response.data);
      } on DioException catch (e) {
        throw handleDioException(e);
      }
    });
  }

  Future<Map<String, dynamic>> authenticateWithGoogle(String idToken) async {
    try {
      Response response = await dio.post(
        'auth/google',
        data: {'idToken': idToken},
      );
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<void> sendFcmTokenToServer(String fcmToken, String accessToken) async {
    try {
      await dio.post(
        'save-fcm-token',
        data: {'fcmToken': fcmToken},
        options: Options(headers: {'Authorization': 'Bearer $accessToken'}),
      );
      debugPrint('FCM token saved successfully');
    } catch (e) {
      debugPrint('Failed to save FCM token');
    }
  }

  Future<void> register(UserLoginReqModel data) async {
    try {
      Response response = await dio.post('register', data: data.toMap());
      debugPrint('register response data: ${response.data}');
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<void> logout() async {
    try {
      Response response = await dio.get('logout');
      debugPrint('logout response data: ${response.data}');
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<void> notifyApiPasswordChanged(
    String email,
    String newPassword,
    String? firebaseToken,
  ) async {
    try {
      Response response = await dio.post(
        'password-reset/notify',
        data: {
          'email': email,
          'new_password': newPassword,
          'firebase_token': firebaseToken,
        },
      );
      debugPrint('API notified about password change: ${response.data}');
    } on DioException catch (e) {
      debugPrint('Failed to notify API about password change: $e');
    }
  }

  // ================================================================
  // GERENCIAMENTO DE USUÁRIOS
  // ================================================================

  Future<Map<String, dynamic>> getUsers({
    int page = 1,
    int perPage = 20,
    String? search,
    String? role,
  }) async {
    return executeWithRetry(() async {
      try {
        final queryParams = <String, dynamic>{'page': page, 'perPage': perPage};

        if (search != null && search.isNotEmpty) {
          queryParams['search'] = search;
        }

        if (role != null && role.isNotEmpty) {
          queryParams['role'] = role;
        }

        Response response = await dio.get(
          'users',
          queryParameters: queryParams,
        );
        AppLogger.logger.debug('getUsers response data: ${response.data}');
        return response.data;
      } on DioException catch (e) {
        throw handleDioException(e);
      }
    });
  }

  Future<UserModel> getUserById(int userId) async {
    return executeWithRetry(() async {
      try {
        Response response = await dio.get('users/$userId');
        AppLogger.logger.debug('getUserById response data: ${response.data}');
        return UserModel.fromMap(response.data['user']);
      } on DioException catch (e) {
        throw handleDioException(e);
      }
    });
  }

  Future<UserModel> createUser(Map<String, dynamic> userData) async {
    return executeWithRetry(() async {
      try {
        Response response = await dio.post('users', data: userData);
        AppLogger.logger.debug('createUser response data: ${response.data}');
        return UserModel.fromMap(response.data['user']);
      } on DioException catch (e) {
        throw handleDioException(e);
      }
    });
  }

  Future<UserModel> updateUser(
    int userId,
    Map<String, dynamic> userData,
  ) async {
    return executeWithRetry(() async {
      try {
        Response response = await dio.put('users/$userId', data: userData);
        AppLogger.logger.debug('updateUser response data: ${response.data}');
        return UserModel.fromMap(response.data['user']);
      } on DioException catch (e) {
        throw handleDioException(e);
      }
    });
  }

  Future<void> deleteUser(int userId) async {
    return executeWithRetry(() async {
      try {
        Response response = await dio.delete('users/$userId');
        AppLogger.logger.debug('deleteUser response data: ${response.data}');
      } on DioException catch (e) {
        throw handleDioException(e);
      }
    });
  }

  Future<void> toggleUserStatus(int userId) async {
    return executeWithRetry(() async {
      try {
        Response response = await dio.post('users/$userId/toggle-status');
        AppLogger.logger.debug(
          'toggleUserStatus response data: ${response.data}',
        );
      } on DioException catch (e) {
        throw handleDioException(e);
      }
    });
  }

  Future<List<UserModel>> fetchUsers() async {
    try {
      Response response = await dio.get('profile');
      AppLogger.logger.debug('fetchUsers response data: ${response.data}');

      if (response.data is List) {
        List<UserModel> users =
            (response.data as List)
                .map(
                  (user) =>
                      user is Map<String, dynamic>
                          ? UserModel.fromMap(user)
                          : null,
                )
                .where((user) => user != null)
                .cast<UserModel>()
                .toList();
        return users;
      } else {
        throw Exception('Resposta da API não é uma lista');
      }
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      throw Exception('Erro ao processar usuários: $e');
    }
  }

  Future<UserProfileModel> updateProfile(UserProfileModel data) async {
    debugPrint("UserApiProvider: updateProfile called");
    try {
      FormData formData = FormData.fromMap({'nome': data.nome});

      if (data.imagemPerfil != null) {
        // Check if it's a local path or a URL
        if (data.imagemPerfil!.startsWith('http')) {
          // If it's a URL, no need to send the file data
          formData.fields.add(MapEntry('imagem_perfil', data.imagemPerfil!));
        } else {
          // If it's a path, send the file as a multipart
          formData.files.add(
            MapEntry(
              'imagem_perfil',
              await MultipartFile.fromFile(data.imagemPerfil!),
            ),
          );
        }
      }

      Response response = await dio.put('profile', data: formData);
      return UserProfileModel.fromMap(response.data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  // ================================================================
  // ROLES/CARGOS
  // ================================================================

  Future<List<Role>> fetchCargos() async {
    try {
      final response = await dio.get('/cargos');
      debugPrint('fetchCargos response: ${response.data}');

      if (response.data is List) {
        return (response.data as List)
            .map((e) => e is Map<String, dynamic> ? Role.fromMap(e) : null)
            .where((role) => role != null)
            .cast<Role>()
            .toList();
      } else {
        throw Exception('Resposta da API não é uma lista');
      }
    } on DioException catch (e) {
      throw handleDioException(e);
    } catch (e) {
      throw Exception('Erro ao processar cargos: $e');
    }
  }

  Future<void> updateUserCargos(int userId, List<int> cargoIds) async {
    try {
      debugPrint('Atualizando cargos do usuário $userId: $cargoIds');
      final response = await dio.post(
        '/usuarios/$userId/cargos',
        data: {'cargos': cargoIds},
      );
      debugPrint('Resposta updateUserCargos: ${response.data}');
    } on DioException catch (e) {
      debugPrint('Erro DioException em updateUserCargos: ${e.message}');
      throw handleDioException(e);
    } catch (e) {
      debugPrint('Erro geral em updateUserCargos: $e');
      throw Exception('Erro ao atualizar cargos do usuário: $e');
    }
  }
}
