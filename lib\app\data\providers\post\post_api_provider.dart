import 'package:dio/dio.dart';
import 'package:ibcb_desktop/app/data/models/posts_model.dart';
import 'package:ibcb_desktop/app/data/providers/base/base_api_provider.dart';

/// Provedor de API para operações relacionadas a posts
/// Inclui posts e comentários de posts
class PostApiProvider extends BaseApiProvider {
  // ================================================================
  // POSTS
  // ================================================================

  Future<Map<String, dynamic>> getPosts({int page = 1, int perPage = 5}) async {
    final response = await dio.get(
      'post',
      queryParameters: {'page': page, 'perPage': perPage},
    );
    return response.data;
  }

  Future<void> createPost(FormData data) async {
    try {
      Response response = await dio.post('post', data: data);
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<PostsModel> updatePost(int id, FormData data) async {
    try {
      Response response = await dio.put('post/$id', data: data);
      return PostsModel.fromMap(response.data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<void> deletePost(int id) async {
    try {
      Response response = await dio.delete('post/$id');
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  // ================================================================
  // COMENTÁRIOS DE POSTS
  // ================================================================

  Future<Map<String, dynamic>> createCommentPost(
    int forumId,
    String content,
  ) async {
    try {
      Response response = await dio.post(
        '/post/$forumId/comment',
        data: {'conteudo': content},
      );
      return response.data; // Retorna os dados do comentário criado
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<dynamic> deleteCommentsPost(int id) async {
    try {
      Response response = await dio.delete('post/$id/comment');
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }
}
