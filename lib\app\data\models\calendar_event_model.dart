import 'dart:convert';

class CalendarEventModel {
  final int? id;
  final String title;
  final String description;
  final DateTime startDate;
  final DateTime endDate;
  final String? location;
  final String? imageUrl;
  final String category; // culto, reunião, etc.
  final int isActive;
  final String createdBy; // Agora string
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<String> participants; // Agora lista de string
  final int? participantCount;
  final bool? isParticipating;

  CalendarEventModel({
    this.id,
    required this.title,
    required this.description,
    required this.startDate,
    required this.endDate,
    this.location,
    this.imageUrl,
    required this.category,
    this.isActive = 1,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.participants = const [],
    this.participantCount,
    this.isParticipating,
  });
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{
      'title': title,
      'description': description,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'category': category,
      'is_active': isActive,
      'created_by': createdBy,
    };
    if (location != null && location!.isNotEmpty) {
      map['location'] = location;
    }
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      map['image_url'] = imageUrl;
    }
    // Serializa participants SEMPRE como string JSON
    map['participants'] = jsonEncode(participants);
    return map;
  }

  factory CalendarEventModel.fromMap(Map<String, dynamic> map) {
    // Trata id e isActive como int, mesmo se vierem como string
    int? parseInt(dynamic value) {
      if (value == null) return null;
      if (value is int) return value;
      return int.tryParse(value.toString());
    }

    // Trata participants como array, mesmo se vier string JSON
    List<String> parseParticipants(dynamic value) {
      if (value == null) return [];
      if (value is List) return List<String>.from(value);
      if (value is String && value.isNotEmpty) {
        try {
          final decoded = jsonDecode(value);
          if (decoded is List) return List<String>.from(decoded);
        } catch (_) {}
      }
      return [];
    }

    return CalendarEventModel(
      id: parseInt(map['id']),
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      startDate: DateTime.parse(map['start_date']),
      endDate: DateTime.parse(map['end_date']),
      location: map['location'],
      imageUrl: map['image_url'],
      category: map['category'] ?? '',
      isActive: parseInt(map['is_active']) ?? 1,
      createdBy: map['created_by']?.toString() ?? '',
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
      participants: parseParticipants(map['participants']),
      participantCount: parseInt(map['participantCount']),
      isParticipating: map['isParticipating'],
    );
  }
  CalendarEventModel copyWith({
    int? id,
    String? title,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    String? location,
    String? imageUrl,
    String? category,
    int? isActive,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? participants,
    int? participantCount,
    bool? isParticipating,
  }) {
    return CalendarEventModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      location: location ?? this.location,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      participants: participants ?? this.participants,
      participantCount: participantCount ?? this.participantCount,
      isParticipating: isParticipating ?? this.isParticipating,
    );
  }

  @override
  String toString() {
    return 'CalendarEventModel(id: $id, title: $title, description: $description, startDate: $startDate, endDate: $endDate, location: $location, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CalendarEventModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Enum para categorias de eventos
enum EventCategory {
  culto('Culto'),
  conexao('Conexão'),
  reuniao('Reunião'),
  evangelismo('Evangelismo'),
  ensino('Ensino'),
  classea('Classe A'),
  oracao('Oração'),
  comunhao('Comunhão'),
  missoes('Missões'),
  juventude('Culto de Jovens'),
  outro('Outro');

  const EventCategory(this.displayName);
  final String displayName;

  static EventCategory fromString(String value) {
    return EventCategory.values.firstWhere(
      (category) => category.name == value,
      orElse: () => EventCategory.outro,
    );
  }
}
