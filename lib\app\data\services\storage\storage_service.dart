import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

enum StorageKey { token }

class StorageService extends GetxService {
  final _box = GetStorage();
  final _token = RxnString();

  String? get token => _token.value;

  @override
  void onInit() {
    super.onInit();
    _token.value = _box.read('${StorageKey.token}');
    debugPrint('================================');
    debugPrint(_token.value);
    debugPrint('================================');
    _box.listenKey(
      StorageKey.token.toString(),
      (value) => _token.value = value,
    );
  }

  Future<void> saveToken(String token) async {
    await _box.write(StorageKey.token.toString(), token);
    _token.value = token;
  }

  Future<void> removeToken() async {
    await _box.remove(StorageKey.token.toString());
    _token.value = null;
  }
}
