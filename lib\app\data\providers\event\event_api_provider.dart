import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:ibcb_desktop/app/data/models/evento_model.dart';
import 'package:ibcb_desktop/app/data/providers/base/base_api_provider.dart';

/// Provedor de API para operações relacionadas a eventos simples
/// Para eventos do calendário, use CalendarApiProvider
class EventApiProvider extends BaseApiProvider {
  Future<List<EventoModel>> getEvent() async {
    try {
      Response response = await dio.get('event');
      List<dynamic> data = response.data as List<dynamic>;
      return data.map((item) => EventoModel.fromMap(item)).toList();
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<void> createEvent(FormData data) async {
    try {
      Response response = await dio.post('event', data: data);
      debugPrint('postEvent response data: ${response.data}');
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<void> deleteEvent(int eventId) async {
    try {
      Response response = await dio.delete('event/$eventId');
      debugPrint('deleteEvent response data: ${response.data}');
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }
}
