import 'package:dio/dio.dart';
import '../base/base_api_provider.dart';
import '../../models/formacao/tema_model.dart';

class TemaApiProvider extends BaseApiProvider {
  Dio get dio => super.dio;

  Future<List<TemaModel>> getTemas() async {
    try {
      final response = await dio.get('/temas');
      if (response.data != null) {
        return (response.data as List)
            .map((item) => TemaModel.fromJson(item))
            .toList();
      }
      return [];
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<List<TemaModel>> getTemasByTipoFormacao(int tipoFormacaoId) async {
    try {
      final response = await dio.get(
        '/temas',
        queryParameters: {'tipo_formacao_id': tipoFormacaoId},
      );
      if (response.data != null) {
        return (response.data as List)
            .map((item) => TemaModel.fromJson(item))
            .toList();
      }
      return [];
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<TemaModel> getTema(int id) async {
    try {
      final response = await dio.get('/temas/$id');
      return TemaModel.fromJson(response.data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<TemaModel> createTema(Map<String, dynamic> data) async {
    try {
      final response = await dio.post('/temas', data: data);
      return TemaModel.fromJson(response.data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<TemaModel> updateTema(int id, Map<String, dynamic> data) async {
    try {
      final response = await dio.put('/temas/$id', data: data);
      return TemaModel.fromJson(response.data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<void> deleteTema(int id) async {
    try {
      await dio.delete('/temas/$id');
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }
}
