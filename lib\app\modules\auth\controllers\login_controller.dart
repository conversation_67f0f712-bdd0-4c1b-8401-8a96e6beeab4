import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/user_login_req_model.dart';
import '../../../data/services/auth/auth_service.dart';
import '../../../helpers/snack_bar_msg.dart';
import '../../../helpers/validators.dart';
import '../../../core/logger/app_logger.dart';

class LoginController extends GetxController {
  static final _logger = AppLogger.logger;
  
  final AuthService _authService = Get.find<AuthService>();
  final SnackBarMsg _snackBarMsg = SnackBarMsg();
  
  final formKey = GlobalKey<FormState>();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  
  final isLoading = false.obs;
  final isPasswordVisible = false.obs;
  
  @override
  void onInit() {
    super.onInit();
    _logger.debug('LoginController initialized');
  }
  
  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }
  
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }
  
  Future<void> login() async {
    if (!formKey.currentState!.validate()) {
      return;
    }
    
    try {
      isLoading.value = true;
      
      final loginRequest = UserLoginReqModel(
        email: emailController.text.trim(),
        password: passwordController.text,
      );
      
      await _authService.login(loginRequest);
      
      // Navigation is handled by AuthService.redirectToProperPage()
      _logger.info('Login successful for user: ${emailController.text}');
      
    } catch (e) {
      _logger.error('Login failed', e);
      _snackBarMsg.showSnack('Erro no login', e.toString());
    } finally {
      isLoading.value = false;
    }
  }
  
  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email é obrigatório';
    }
    if (!emailValid(value)) {
      return 'Email inválido';
    }
    return null;
  }
  
  String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Senha é obrigatória';
    }
    if (value.length < 6) {
      return 'Senha deve ter pelo menos 6 caracteres';
    }
    return null;
  }
}
