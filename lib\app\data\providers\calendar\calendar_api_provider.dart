import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:ibcb_desktop/app/data/providers/base/base_api_provider.dart';

/// Provedor de API para operações relacionadas ao calendário de eventos
/// Calendário com funcionalidades avançadas como participação, categorias, etc.
class CalendarApiProvider extends BaseApiProvider {
  // ================================================================
  // EVENTOS DO CALENDÁRIO
  // ================================================================

  /// Buscar todos os eventos do calendário
  Future<Map<String, dynamic>> getCalendarEvents({
    int page = 1,
    int perPage = 10,
  }) async {
    try {
      Response response = await dio.get(
        'calendar/events',
        queryParameters: {'page': page, 'perPage': perPage},
      );
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  /// Criar um novo evento no calendário
  Future<Map<String, dynamic>> createCalendarEvent(FormData data) async {
    try {
      Response response = await dio.post('calendar/events', data: data);
      return response.data;
    } on DioException catch (e) {
      debugPrint('Erro ao criar evento: ${e.message}');
      debugPrint('Erro ao criar evento: ${e.response?.data}');
      debugPrint('Erro ao criar evento: ${e.response?.statusCode}');
      debugPrint('Erro ao criar evento: ${e.response?.statusMessage}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.uri}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.method}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.headers}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.data}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.queryParameters}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.connectTimeout}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.receiveTimeout}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.sendTimeout}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.followRedirects}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.maxRedirects}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.validateStatus}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.responseType}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.contentType}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.responseType}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.extra}');
      debugPrint('Erro ao criar evento: ${e.requestOptions.baseUrl}');

      throw handleDioException(e);
    }
  }

  /// Buscar um evento específico por ID
  Future<Map<String, dynamic>> getCalendarEventById(int eventId) async {
    try {
      Response response = await dio.get('calendar/events/$eventId');
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  /// Atualizar um evento do calendário
  Future<Map<String, dynamic>> updateCalendarEvent(
    int eventId,
    FormData data,
  ) async {
    try {
      Response response = await dio.put('calendar/events/$eventId', data: data);
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  /// Deletar um evento do calendário
  Future<void> deleteCalendarEvent(int eventId) async {
    try {
      await dio.delete('calendar/events/$eventId');
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  // ================================================================
  // PARTICIPAÇÃO EM EVENTOS
  // ================================================================

  /// Participar de um evento
  Future<Map<String, dynamic>> participateInEvent(int eventId) async {
    try {
      Response response = await dio.post(
        'calendar/events/$eventId/participate',
      );
      return response.data;
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  /// Sair de um evento
  Future<void> leaveEvent(int eventId) async {
    try {
      await dio.delete('calendar/events/$eventId/participate');
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  // ================================================================
  // CATEGORIAS E FILTROS
  // ================================================================

  /// Buscar categorias de eventos
  Future<List<Map<String, dynamic>>> getCalendarCategories() async {
    try {
      Response response = await dio.get('calendar/categories');
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  /// Buscar eventos por data específica
  Future<List<Map<String, dynamic>>> getCalendarEventsByDate(
    DateTime date,
  ) async {
    try {
      Response response = await dio.get(
        'calendar/events/by-date',
        queryParameters: {'date': date.toIso8601String().split('T')[0]},
      );
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  /// Buscar próximos eventos
  Future<List<Map<String, dynamic>>> getUpcomingCalendarEvents({
    int limit = 5,
  }) async {
    try {
      Response response = await dio.get(
        'calendar/events/upcoming',
        queryParameters: {'limit': limit},
      );
      return List<Map<String, dynamic>>.from(response.data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }
}
