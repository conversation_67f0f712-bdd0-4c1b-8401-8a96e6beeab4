class PostCreateReqModel {
  final String conteudo;
  final String? imagem;

  PostCreateReqModel({required this.conteudo, this.imagem});

  Map<String, dynamic> toMap() {
    return {'conteudo': conteudo, if (imagem != null) 'imagem': imagem};
  }
}

class PostUpdateReqModel {
  final String conteudo;
  final String? imagem;

  PostUpdateReqModel({required this.conteudo, this.imagem});

  Map<String, dynamic> toMap() {
    return {'conteudo': conteudo, if (imagem != null) 'imagem': imagem};
  }
}
