import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

/// Centralized logger configuration for the IBCB app
class AppLogger {
  static Logger? _logger;
  static String? _currentModule;

  /// Get the configured logger instance
  static Logger get logger {
    _logger ??= _createLogger();
    return _logger!;
  }

  /// Create and configure the logger
  static Logger _createLogger() {
    return Logger(
      filter: _AppLogFilter(),
      printer: _AppLogPrinter(),
      output: _AppLogOutput(),
    );
  }

  /// Set the current module/component for contextual logging
  static void setModule(String module) {
    _currentModule = module;
  }

  /// Clear the current module context
  static void clearModule() {
    _currentModule = null;
  }

  /// Get a logger instance with module context
  static Logger getModuleLogger(String module) {
    setModule(module);
    return logger;
  }

  /// Reset the logger (useful for testing)
  static void reset() {
    _logger = null;
    _currentModule = null;
  }
}

/// Custom log filter that respects debug/release mode
class _AppLog<PERSON>ilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    // In release mode, only log warnings and errors
    if (kReleaseMode) {
      return event.level.index >= Level.warning.index;
    }
    // In debug mode, log everything
    return true;
  }
}

/// Enhanced log printer with advanced formatting and visual improvements
class _AppLogPrinter extends LogPrinter {
  static const int errorMethodCount = 8;
  static const int lineLength = 120;
  static const String topLeftCorner = '┌';
  static const String bottomLeftCorner = '└';
  static const String middleCorner = '├';
  static const String verticalLine = '│';
  static const String doubleDivider = '┄';
  static const String singleDivider = '┈';

  static final _levelEmojis = {
    Level.trace: '🔍',
    Level.debug: '🐛',
    Level.info: 'ℹ️',
    Level.warning: '⚠️',
    Level.error: '❌',
    Level.fatal: '💀',
  };

  static final _levelColors = {
    Level.trace: '\x1B[37m', // White
    Level.debug: '\x1B[36m', // Cyan
    Level.info: '\x1B[32m', // Green
    Level.warning: '\x1B[33m', // Yellow
    Level.error: '\x1B[31m', // Red
    Level.fatal: '\x1B[35m', // Magenta
  };

  static const String _resetColor = '\x1B[0m';
  static const String _grayColor = '\x1B[90m';
  static const String _boldColor = '\x1B[1m';

  @override
  List<String> log(LogEvent event) {
    final color = _levelColors[event.level] ?? '';
    final emoji = _levelEmojis[event.level] ?? '';
    final level = event.level.name.toUpperCase();
    final time = _formatTime(DateTime.now());
    final module = AppLogger._currentModule;

    final message = event.message;
    final error = event.error;
    final stackTrace = event.stackTrace;

    final lines = <String>[];

    // Top border
    lines.add(
      '$color$topLeftCorner${_repeat(doubleDivider, lineLength - 1)}$_resetColor',
    );

    // Header with timestamp, level, and module
    final moduleInfo = module != null ? ' [$module]' : '';
    final header = '$emoji $level$moduleInfo';
    lines.add(
      '$color$verticalLine$_resetColor $_grayColor$time$_resetColor $color$_boldColor$header$_resetColor',
    );

    // Separator
    lines.add(
      '$color$middleCorner${_repeat(singleDivider, lineLength - 1)}$_resetColor',
    );

    // Message content
    final messageLines = _formatMessage(message);
    for (final line in messageLines) {
      lines.add('$color$verticalLine$_resetColor $line');
    }

    // Error information if present
    if (error != null) {
      lines.add(
        '$color$middleCorner${_repeat(singleDivider, lineLength - 1)}$_resetColor',
      );
      lines.add(
        '$color$verticalLine$_resetColor ${_levelColors[Level.error]}ERROR: $error$_resetColor',
      );
    }

    // Stack trace if present (only in debug mode and for errors/warnings)
    if (stackTrace != null &&
        kDebugMode &&
        (event.level == Level.error ||
            event.level == Level.warning ||
            event.level == Level.fatal)) {
      lines.add(
        '$color$middleCorner${_repeat(singleDivider, lineLength - 1)}$_resetColor',
      );
      final stackLines = _formatStackTrace(stackTrace);
      for (final line in stackLines) {
        lines.add(
          '$color$verticalLine$_resetColor $_grayColor$line$_resetColor',
        );
      }
    }

    // Bottom border
    lines.add(
      '$color$bottomLeftCorner${_repeat(doubleDivider, lineLength - 1)}$_resetColor',
    );

    // Add empty line for better separation
    lines.add('');

    return lines;
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:'
        '${time.minute.toString().padLeft(2, '0')}:'
        '${time.second.toString().padLeft(2, '0')}.'
        '${time.millisecond.toString().padLeft(3, '0')}';
  }

  List<String> _formatMessage(dynamic message) {
    final messageStr = message.toString();
    final lines = <String>[];

    // Handle multi-line messages
    final messageLines = messageStr.split('\n');

    for (final line in messageLines) {
      if (line.length <= lineLength - 4) {
        lines.add(line);
      } else {
        // Break long lines
        final words = line.split(' ');
        String currentLine = '';

        for (final word in words) {
          if ((currentLine + word).length <= lineLength - 4) {
            currentLine += (currentLine.isEmpty ? '' : ' ') + word;
          } else {
            if (currentLine.isNotEmpty) {
              lines.add(currentLine);
              currentLine = word;
            } else {
              // Word is too long, break it
              lines.add('${word.substring(0, lineLength - 7)}...');
              currentLine = '';
            }
          }
        }

        if (currentLine.isNotEmpty) {
          lines.add(currentLine);
        }
      }
    }

    return lines.isEmpty ? [''] : lines;
  }

  List<String> _formatStackTrace(StackTrace stackTrace) {
    final lines = stackTrace.toString().split('\n');
    final formattedLines = <String>[];

    int count = 0;
    for (final line in lines) {
      if (line.trim().isEmpty) continue;
      if (count >= errorMethodCount) break;

      // Extract meaningful parts of stack trace
      final trimmedLine = line.trim();
      if (trimmedLine.startsWith('#')) {
        // Format stack frame
        final parts = trimmedLine.split(' ');
        if (parts.length >= 2) {
          final frameNumber = parts[0];
          final method = parts[1];
          final location = parts.length > 2 ? parts.sublist(2).join(' ') : '';

          formattedLines.add('$frameNumber $method');
          if (location.isNotEmpty) {
            formattedLines.add('    $location');
          }
        } else {
          formattedLines.add(trimmedLine);
        }
        count++;
      }
    }

    return formattedLines;
  }

  String _repeat(String char, int count) {
    return char * count;
  }
}

/// Custom log output that handles console output
class _AppLogOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    for (final line in event.lines) {
      // Use debugPrint to ensure output in Flutter
      debugPrint(line);
    }
  }
}

/// Convenience methods for logging with enhanced functionality
extension AppLoggerExtension on Logger {
  /// Log debug information with context
  void debug(String message, [dynamic error, StackTrace? stackTrace]) {
    d(message, error: error, stackTrace: stackTrace);
  }

  /// Log info with context
  void info(String message, [dynamic error, StackTrace? stackTrace]) {
    i(message, error: error, stackTrace: stackTrace);
  }

  /// Log warning with context
  void warning(String message, [dynamic error, StackTrace? stackTrace]) {
    w(message, error: error, stackTrace: stackTrace);
  }

  /// Log error with context
  void error(String message, [dynamic error, StackTrace? stackTrace]) {
    e(message, error: error, stackTrace: stackTrace);
  }

  /// Log method entry for debugging
  void methodEntry(String methodName, [Map<String, dynamic>? parameters]) {
    final paramStr =
        parameters != null && parameters.isNotEmpty
            ? ' with params: ${parameters.toString()}'
            : '';
    d('→ Entering $methodName$paramStr');
  }

  /// Log method exit for debugging
  void methodExit(String methodName, [dynamic result]) {
    final resultStr = result != null ? ' returning: $result' : '';
    d('← Exiting $methodName$resultStr');
  }

  /// Log API request
  void apiRequest(String method, String url, [Map<String, dynamic>? data]) {
    final dataStr = data != null ? '\nData: ${data.toString()}' : '';
    i('🌐 API $method $url$dataStr');
  }

  /// Log API response
  void apiResponse(String method, String url, int statusCode, [dynamic data]) {
    final statusEmoji = statusCode >= 200 && statusCode < 300 ? '✅' : '❌';
    final dataStr = data != null ? '\nResponse: ${data.toString()}' : '';
    i('$statusEmoji API $method $url [$statusCode]$dataStr');
  }

  /// Log Firebase operation
  void firebaseOperation(String operation, String collection, [String? docId]) {
    final docStr = docId != null ? '/$docId' : '';
    i('🔥 Firebase $operation: $collection$docStr');
  }

  /// Log user action
  void userAction(String action, [Map<String, dynamic>? context]) {
    final contextStr = context != null ? ' - ${context.toString()}' : '';
    i('👤 User action: $action$contextStr');
  }

  /// Log performance measurement
  void performance(String operation, Duration duration, [String? details]) {
    final detailsStr = details != null ? ' - $details' : '';
    final ms = duration.inMilliseconds;
    final emoji =
        ms < 100
            ? '⚡'
            : ms < 500
            ? '⏱️'
            : '🐌';
    i('$emoji Performance: $operation took ${ms}ms$detailsStr');
  }

  /// Log data structure with pretty formatting
  void data(String label, dynamic data) {
    if (data is Map || data is List) {
      d('📊 $label:\n${_prettyPrintData(data)}');
    } else {
      d('📊 $label: $data');
    }
  }

  String _prettyPrintData(dynamic data, [int indent = 0]) {
    final spaces = '  ' * indent;

    if (data is Map) {
      final buffer = StringBuffer('{\n');
      data.forEach((key, value) {
        buffer.write('$spaces  $key: ');
        if (value is Map || value is List) {
          buffer.write(_prettyPrintData(value, indent + 1));
        } else {
          buffer.write(value.toString());
        }
        buffer.write(',\n');
      });
      buffer.write('$spaces}');
      return buffer.toString();
    } else if (data is List) {
      final buffer = StringBuffer('[\n');
      for (int i = 0; i < data.length; i++) {
        buffer.write('$spaces  ');
        if (data[i] is Map || data[i] is List) {
          buffer.write(_prettyPrintData(data[i], indent + 1));
        } else {
          buffer.write(data[i].toString());
        }
        if (i < data.length - 1) buffer.write(',');
        buffer.write('\n');
      }
      buffer.write('$spaces]');
      return buffer.toString();
    }

    return data.toString();
  }
}

/// Module-specific logger helpers
class ModuleLogger {
  final Logger _logger;

  ModuleLogger._(this._logger);

  factory ModuleLogger.create(String module) {
    AppLogger.setModule(module);
    return ModuleLogger._(AppLogger.logger);
  }

  void debug(String message, [dynamic error, StackTrace? stackTrace]) =>
      _logger.debug(message, error, stackTrace);

  void info(String message, [dynamic error, StackTrace? stackTrace]) =>
      _logger.info(message, error, stackTrace);

  void warning(String message, [dynamic error, StackTrace? stackTrace]) =>
      _logger.warning(message, error, stackTrace);

  void error(String message, [dynamic error, StackTrace? stackTrace]) =>
      _logger.error(message, error, stackTrace);

  void methodEntry(String methodName, [Map<String, dynamic>? parameters]) =>
      _logger.methodEntry(methodName, parameters);

  void methodExit(String methodName, [dynamic result]) =>
      _logger.methodExit(methodName, result);

  void apiRequest(String method, String url, [Map<String, dynamic>? data]) =>
      _logger.apiRequest(method, url, data);

  void apiResponse(String method, String url, int statusCode, [dynamic data]) =>
      _logger.apiResponse(method, url, statusCode, data);

  void firebaseOperation(
    String operation,
    String collection, [
    String? docId,
  ]) => _logger.firebaseOperation(operation, collection, docId);

  void userAction(String action, [Map<String, dynamic>? context]) =>
      _logger.userAction(action, context);

  void performance(String operation, Duration duration, [String? details]) =>
      _logger.performance(operation, duration, details);

  void data(String label, dynamic data) => _logger.data(label, data);
}
