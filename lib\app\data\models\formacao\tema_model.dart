class TemaModel {
  final int id;
  final int tipoFormacaoId;
  final String nome;
  final String descricao;
  final String? imagem;
  final int ordem;
  final DateTime createdAt;
  final DateTime updatedAt;

  TemaModel({
    required this.id,
    required this.tipoFormacaoId,
    required this.nome,
    required this.descricao,
    this.imagem,
    required this.ordem,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TemaModel.fromJson(Map<String, dynamic> json) {
    return TemaModel(
      id: json['id'] ?? 0,
      tipoFormacaoId: json['tipo_formacao_id'] ?? json['tipoFormacaoId'] ?? 0,
      nome: json['nome'] ?? '',
      descricao: json['descricao'] ?? '',
      imagem: json['imagem'],
      ordem: json['ordem'] ?? 0,
      createdAt: DateTime.parse(
        json['created_at'] ??
            json['createdAt'] ??
            DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updated_at'] ??
            json['updatedAt'] ??
            DateTime.now().toIso8601String(),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tipo_formacao_id': tipoFormacaoId,
      'nome': nome,
      'descricao': descricao,
      'imagem': imagem,
      'ordem': ordem,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  TemaModel copyWith({
    int? id,
    int? tipoFormacaoId,
    String? nome,
    String? descricao,
    String? imagem,
    int? ordem,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TemaModel(
      id: id ?? this.id,
      tipoFormacaoId: tipoFormacaoId ?? this.tipoFormacaoId,
      nome: nome ?? this.nome,
      descricao: descricao ?? this.descricao,
      imagem: imagem ?? this.imagem,
      ordem: ordem ?? this.ordem,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
