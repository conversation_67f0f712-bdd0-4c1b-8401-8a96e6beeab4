import 'package:ibcb_desktop/app/data/models/user_model.dart';

class ForumCommentModel {
  final int id;
  final int forumId;
  final int userId;
  final String conteudo;
  final DateTime criado;
  final UserModel user;
  final int? parentId;
  ForumCommentModel({
    required this.id,
    required this.forumId,
    required this.userId,
    required this.conteudo,
    required this.criado,
    required this.user,
    this.parentId,
  });
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'forumId': forumId,
      'userId': userId,
      'conteudo': conteudo,
      'criado': criado.millisecondsSinceEpoch,
      'user': user.toMap(),
      'parentId': parentId,
    };
  }

  factory ForumCommentModel.fromMap(Map<String, dynamic> map) {
    try {
      // Validação explícita do tipo String
      String conteudo = map['conteudo']?.toString() ?? '';
      return ForumCommentModel(
        id: map['id']?.toInt() ?? 0,
        forumId: map['forum_id']?.toInt() ?? 0,
        userId: map['user_id']?.toInt() ?? 0,
        conteudo: conteudo,
        criado: DateTime.parse(map['criado']),
        user: UserModel.fromMap(map['user']),
        parentId: map['parent_id']?.toInt(),
      );
    } catch (e) {
      print('Erro ao converter ForumCommentModel: $e');
      rethrow;
    }
  }
}
