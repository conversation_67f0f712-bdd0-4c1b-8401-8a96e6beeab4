// import 'package:firebase_auth/firebase_auth.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:get/get.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:ibcb_desktop/app/data/models/user_login_req_model.dart';
import 'package:ibcb_desktop/app/data/models/user_model.dart';
import 'package:ibcb_desktop/app/data/models/user_profile_model.dart';
import 'package:ibcb_desktop/app/data/services/auth/auth_repository.dart';
import 'package:ibcb_desktop/app/data/services/storage/storage_service.dart';
import 'package:ibcb_desktop/app/routes/pages.dart';

class AuthService extends GetxService {
  final _storageService = Get.find<StorageService>();
  final AuthRepository _repository;
  final user = Rxn<UserModel>();
  final userProfile = Rxn<UserProfileModel>();
  // final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  bool get isLoggedIn => user.value != null;

  AuthService(this._repository);

  @override
  Future<void> onInit() async {
    super.onInit();
    await getUser();
  }

  // Future<void> saveToken(String token) async {
  //   await _storageService.saveToken(token);
  // }

  Future<void> login(UserLoginReqModel userLoginReq) async {
    // String? currentUid = FirebaseAuth.instance.currentUser?.email;

    var userLoginRes = await _repository.login(userLoginReq);
    await _storageService.saveToken(userLoginRes.token);

    // FirebaseMessaging.instance.onTokenRefresh.listen((newToken) async {
    //   await _repository.sendFcmTokenToServer(newToken, userLoginRes.token);
    // });
    // String? fcmToken = await FirebaseMessaging.instance.getToken();

    // if (fcmToken != null) {
    //   await _repository.sendFcmTokenToServer(fcmToken, userLoginRes.token);
    // }
    debugPrint(
      '=======userLoginRes.token=========================================================',
    );
    debugPrint(userLoginRes.token);
    debugPrint(
      '==fcmToken==============================================================',
    );
    // debugPrint(fcmToken);
    debugPrint(
      '=currentUid===============================================================',
    );
    //debugPrint(currentUid);
    debugPrint(
      '================================================================',
    );
    await getUser();
    redirectToProperPage();
  }

  // Future<void> authenticateWithGoogle() async {
  //   try {
  //     GoogleSignInAccount? googleUser;

  //     // Verificar se estamos em ambiente web
  //     bool isWeb = kIsWeb;

  //     if (isWeb) {
  //       // Para web, configurar o GoogleSignIn com suporte ao FedCM
  //       final GoogleSignIn webGoogleSignIn = GoogleSignIn(
  //         // Configurações para FedCM
  //         clientId: 'SEU_CLIENT_ID_WEB', // Substitua pelo seu client ID real
  //         scopes: ['email', 'profile'],
  //       );

  //       try {
  //         // Tentar login com FedCM
  //         googleUser = await webGoogleSignIn.signIn();
  //         if (googleUser == null) {
  //           debugPrint("Login cancelado pelo usuário ou FedCM não disponível.");
  //           return;
  //         }
  //       } catch (e) {
  //         debugPrint("Erro ao autenticar com FedCM: $e");
  //         return;
  //       }
  //     } else {
  //       // Para mobile, continuar usando signIn normalmente
  //       googleUser = await _googleSignIn.signIn();
  //       if (googleUser == null) {
  //         debugPrint("Login cancelado pelo usuário.");
  //         return;
  //       }
  //     }

  //     final GoogleSignInAuthentication googleAuth =
  //         await googleUser.authentication;

  //     final AuthCredential credential = GoogleAuthProvider.credential(
  //       accessToken: googleAuth.accessToken,
  //       idToken: googleAuth.idToken,
  //     );

  //     await _processGoogleCredential(credential);
  //   } catch (e) {
  //     debugPrint("Erro ao autenticar com Google: $e");
  //   }
  // }

  // Método para processar o credential do Google obtido via web ou mobile
  // Future<void> _processGoogleCredential(AuthCredential credential) async {
  //   try {
  //     final UserCredential userCredential = await _auth.signInWithCredential(
  //       credential,
  //     );
  //     final String? idToken = await userCredential.user?.getIdToken();

  //     if (idToken != null) {
  //       final response = await _repository.authenticateWithGoogle(idToken);
  //       await _storageService.saveToken(response['accessToken']);

  //       String? fcmToken = await FirebaseMessaging.instance.getToken();
  //       if (fcmToken != null) {
  //         await _repository.sendFcmTokenToServer(
  //           fcmToken,
  //           response['accessToken'],
  //         );
  //       }

  //       await getUser();
  //       redirectToProperPage();
  //     } else {
  //       debugPrint("Erro: idToken é nulo");
  //     }
  //   } catch (e) {
  //     debugPrint("Erro ao processar credencial do Google: $e");
  //   }
  // }

  // Método para autenticar com Google via web usando o idToken obtido pelo botão renderButton
  // Future<void> authenticateWithGoogleWeb(String idToken) async {
  //   try {
  //     // Criar uma credencial com o idToken recebido do botão web
  //     final AuthCredential credential = GoogleAuthProvider.credential(
  //       idToken: idToken,
  //     );

  //     await _processGoogleCredential(credential);
  //   } catch (e) {
  //     debugPrint("Erro ao autenticar com Google Web: $e");
  //   }
  // }

  Future<void> register(UserLoginReqModel userLoginReq) async {
    await _repository.register(userLoginReq);
  }

  Future<void> logout() async {
    try {
      if (user.value!.tipo_conta == 'google') {
        try {
          await _googleSignIn.disconnect();
          debugPrint('Desconectado do Google com sucesso');
        } catch (e) {
          // Tratamento específico para o erro PlatformException com código 'status'
          if (e.toString().contains('PlatformException(status') &&
              e.toString().contains('Failed to disconnect')) {
            debugPrint(
              'Aviso: Erro ao desconectar do Google, mas continuando o processo de logout',
            );
          } else {
            debugPrint('Erro ao desconectar do Google: $e');
          }
          // Continuar com o processo de logout mesmo com erro na desconexão
        }

        try {
          await _googleSignIn.signOut();
          debugPrint('Deslogado do Google com sucesso');
        } catch (e) {
          debugPrint('Erro ao fazer signOut do Google: $e');
          // Continuar com o processo de logout mesmo com erro no signOut
        }
      }
      //else if (user.value!.tipo_conta == 'email') {
      //   await _auth.signOut();
      //  debugPrint(
      //       '================================================================');
      //  debugPrint('deslogou do email');
      //  debugPrint(
      //       '================================================================');
      // }

      await _storageService.removeToken();
      // await FirebaseMessaging.instance.deleteToken();
      await _repository.logout();
      user.value = null;
      Get.offAllNamed(Routes.splashScreen);
      // _redirectToProperPage();
    } on Exception catch (e) {
      debugPrint(
        '================================================================',
      );
      debugPrint(e.toString());
      debugPrint(
        '================================================================',
      );
    }
  }

  Future<void> getUser() async {
    try {
      var fetchedUser = await _repository.getUser();
      if (fetchedUser != null) {
        user.value = fetchedUser;
        debugPrint(
          '-------------User fetched successfully: ${user.value?.toMap()}',
        );
      } else {
        debugPrint("Erro: Usuário não encontrado");
      }
    } catch (e) {
      debugPrint('------------Error fetching user: $e');
      await _storageService.removeToken();
      await _googleSignIn.disconnect();
      await _googleSignIn.signOut();
      await _repository.logout();
      // await FirebaseMessaging.instance.deleteToken();
      Get.offAllNamed(Routes.login);
    }
  }

  void redirectToProperPage() {
    if (user.value != null) {
      Get.offAllNamed(Routes.dashboard);
      // if (user.value!.nome != null && user.value!.imagemPerfil != null) {
      // } else {
      //   // Get.offAllNamed(Routes.initialProfile);
      // }
    }
  }

  Future<void> updateProfile(UserProfileModel userProfileModel) async {
    try {
      await _repository.updateProfile(userProfileModel);
      await getUser();
    } catch (e) {
      Text('Não foi possível atualizar o perfil: $e');
    }
  }
}
