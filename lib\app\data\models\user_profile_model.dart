class UserProfileModel {
  final String nome;
  final String? imagemPerfil;

  UserProfileModel({
    required this.nome,
    this.imagemPerfil,
  });

  Map<String, dynamic> toMap() {
    return {
      'nome': nome,
      'imagem_perfil': imagemPerfil,
    };
  }

  factory UserProfileModel.fromMap(Map<String, dynamic> map) {
    return UserProfileModel(
      nome: map['nome'],
      imagemPerfil: map['imagem_perfil'],
    );
  }
}
