name: ibcb_desktop
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  # firebase_core: ^3.14.0  
  # firebase_auth: ^5.6.0  
  # cloud_firestore: ^5.6.9
  # firebase_storage: ^12.4.7  
  # firebase_messaging: ^15.2.7  

  # Auth
  openid_client: ^0.4.9
  oauth2: ^2.0.3
  google_sign_in: ^6.3.0

  # GET
  get: ^4.7.2
  get_storage: ^2.1.1
  
  # Image
  cached_network_image: ^3.4.1
  image_picker: ^0.8.9
  flutter_image_compress: ^2.4.0
  flutter_svg: ^2.1.0
  photo_view: ^0.15.0
  transparent_image: ^2.0.1
  lottie: ^3.3.1

  # Path
  file_picker: ^9.2.1  # Versão ajustada para compatibilidade com SDK 3.3.0
  path: ^1.9.1
  path_provider: ^2.1.5
  external_path: ^2.2.0
  open_file: ^3.5.10

  # DB
  shared_preferences: ^2.5.3  # Versão ajustada para compatibilidade com SDK 3.3.0
  sqflite: ^2.4.2
  sqflite_common_ffi: ^2.3.6
  sqflite_common_ffi_web: ^1.0.0
  sqlite3_flutter_libs: ^0.5.34

  # Widgets
  flutter_slider_drawer: ^3.0.2
  flutter_animation_progress_bar: ^2.3.1
  loading_animation_widget: ^1.3.0
  social_login_buttons: ^1.0.7
  flutter_advanced_drawer: ^1.5.0

  url_launcher: ^6.3.1
  youtube_player_flutter: ^9.1.1
  youtube_player_iframe: ^5.2.1
  easy_localization: ^3.0.7
  http: ^1.4.0
  intl: ^0.19.0
  dio: ^5.8.0+1 
  permission_handler: ^11.4.0 
  flutter_pdfview: ^1.4.0 
  flutter_downloader: ^1.12.0
  window_manager: ^0.4.3
  device_preview_plus: ^2.3.0
  in_app_update: ^4.2.3
  package_info_plus: ^8.3.0
  flutter_quill: ^10.8.5

  # flutter_inappwebview: 6.1.5 

  # lite_rolling_switch: ^1.0.1

  # timeline_tile: ^2.0.0
  timeline_tile: ^2.0.0
  pdf: ^3.11.3
  sqlite3: ^2.7.6
  logger: ^2.5.0
  
  # Calendar
  table_calendar: ^3.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
