# IBCB Desktop - Sistema de Gerenciamento da Igreja

Um aplicativo desktop completo para gerenciamento de igrejas desenvolvido em Flutter com arquitetura GetX.

## 🚀 Características

### ✅ Implementado
- **Sistema de Autenticação**: Login seguro com validação
- **Dashboard Principal**: Interface moderna com estatísticas em tempo real
- **Gerenciamento de Usuários**: CRUD completo com sistema de cargos
- **Arquitetura GetX**: Estado reativo, injeção de dependência e roteamento
- **API Modular**: Providers organizados por domínio
- **Interface Responsiva**: Design adaptado para desktop
- **Tema Dinâmico**: Suporte a tema claro e escuro

### 🚧 Em Desenvolvimento
- **Posts**: Sistema de publicações da igreja
- **Fórum**: Discussões e tópicos da comunidade
- **Eventos**: Gerenciamento de eventos e atividades
- **Calendário**: Calendário integrado com eventos
- **Formação**: Sistema de cursos e estudos
- **Finanças**: Controle financeiro da igreja

## 📁 Estrutura do Projeto

```
lib/
├── app/
│   ├── core/
│   │   ├── bindings/          # Bindings globais
│   │   ├── logger/            # Sistema de logging
│   │   └── theme/             # Configuração de temas
│   ├── data/
│   │   ├── models/            # Modelos de dados
│   │   ├── providers/         # Providers de API organizados por domínio
│   │   └── services/          # Serviços (Auth, Storage, etc.)
│   ├── helpers/               # Utilitários e validadores
│   ├── modules/               # Módulos organizados por feature
│   │   ├── auth/              # Autenticação
│   │   ├── dashboard/         # Dashboard principal
│   │   ├── users/             # Gerenciamento de usuários
│   │   ├── posts/             # Sistema de posts
│   │   ├── forum/             # Fórum da comunidade
│   │   ├── events/            # Eventos
│   │   ├── calendar/          # Calendário
│   │   ├── formation/         # Formação e estudos
│   │   └── finance/           # Finanças
│   ├── routes/                # Configuração de rotas
│   ├── utils/                 # Utilitários gerais
│   └── widgets/               # Widgets reutilizáveis
└── main.dart                  # Ponto de entrada da aplicação
```

## 🏗️ Arquitetura

### GetX Pattern
Cada módulo segue o padrão GetX com:
- **Controllers**: Lógica de negócio e estado
- **Views**: Interface do usuário
- **Bindings**: Injeção de dependência

### API Provider Pattern
APIs organizadas por domínio:
- `UserApiProvider`: Usuários e autenticação
- `ForumApiProvider`: Fórum e comentários
- `PostApiProvider`: Posts e comentários
- `EventApiProvider`: Eventos
- `CalendarApiProvider`: Calendário
- `FormacaoApiProvider`: Formação e estudos

## 🚀 Como Executar

### Pré-requisitos
- Flutter SDK 3.7.0+
- Dart 3.0+
- Firebase configurado
- Windows 10+ (para desktop)

### Instalação
```bash
# Clone o repositório
git clone <repository-url>
cd ibcb_desktop

# Instale as dependências
flutter pub get

# Execute a aplicação
flutter run -d windows
```

### Configuração do Firebase
1. Configure o Firebase para sua aplicação
2. Baixe o arquivo `google-services.json` (Android) e `GoogleService-Info.plist` (iOS)
3. Configure as credenciais no projeto

## 📱 Funcionalidades Principais

### 1. Sistema de Autenticação
- Login com email/senha
- Integração com Firebase Auth
- Gerenciamento de sessão
- Redirecionamento automático

### 2. Dashboard
- Estatísticas em tempo real
- Ações rápidas
- Atividades recentes
- Interface moderna e responsiva

### 3. Gerenciamento de Usuários
- Lista de usuários com filtros
- Atribuição de cargos
- Busca e paginação
- CRUD completo

### 4. Navegação Lateral
- Menu organizado por módulos
- Controle de acesso baseado em roles
- Design moderno com ícones

## 🛠️ Tecnologias Utilizadas

### Framework & UI
- **Flutter**: Framework de desenvolvimento
- **GetX**: Gerenciamento de estado, roteamento e DI
- **Material Design 3**: Sistema de design

### Backend & APIs
- **Firebase**: Autenticação e serviços
- **Dio**: Cliente HTTP
- **Custom API**: Backend próprio da igreja

### Armazenamento
- **GetStorage**: Armazenamento local
- **SharedPreferences**: Preferências do usuário
- **SQLite**: Banco de dados local

### Utilitários
- **Logger**: Sistema de logs
- **Validators**: Validação de formulários
- **Image Picker**: Seleção de imagens
- **URL Launcher**: Abertura de URLs

## 🔐 Sistema de Permissões

### Níveis de Acesso
- **Admin (ID: 1)**: Acesso total ao sistema
- **Moderador (ID: 2)**: Acesso a posts, fórum, eventos
- **Usuário (ID: 3)**: Acesso básico

### Controle de Rotas
```dart
// Exemplo de controle de acesso
bool canAccessRoute(MenuItem menuItem) {
  if (currentUser == null) return false;
  if (menuItem.roles.isEmpty) return true;
  
  return currentUser!.cargos?.any((role) => 
    menuItem.roles.contains(role.id)
  ) ?? false;
}
```

## 📝 Próximos Passos

### Alta Prioridade
1. Implementar CRUD completo de Posts
2. Sistema de Fórum com comentários aninhados
3. Gerenciamento de Eventos com categorias
4. Calendário integrado com eventos

### Média Prioridade
1. Sistema de Formação com cursos
2. Controle Financeiro
3. Relatórios e dashboards avançados
4. Sistema de notificações

### Baixa Prioridade
1. Modo offline
2. Sincronização de dados
3. Exportação de relatórios
4. Sistema de backup

## 🐛 Problemas Conhecidos

1. **Firebase Desktop**: Algumas funcionalidades podem ter limitações no desktop
2. **Responsive Design**: Otimização para diferentes tamanhos de tela
3. **Performance**: Otimização para grandes volumes de dados

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Suporte

Para suporte e dúvidas, entre em contato:
- Email: <EMAIL>
- WhatsApp: (11) 99999-9999

---

**IBCB Desktop** - Sistema de Gerenciamento da Igreja
Desenvolvido com ❤️ para a comunidade IBCB
