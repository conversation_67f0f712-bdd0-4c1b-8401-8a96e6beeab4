part of 'pages.dart';

abstract class Routes {
  static const splashScreen = '/';
  static const login = '/login';
  static const home = '/home';
  static const dashboard = '/dashboard';
  
  // User Management
  static const users = '/users';
  static const userCreate = '/users/create';
  static const userEdit = '/users/edit';
  static const userRoles = '/users/roles';
  
  // Forum Management
  static const forum = '/forum';
  static const forumCreate = '/forum/create';
  static const forumEdit = '/forum/edit';
  static const forumView = '/forum/view';
  
  // Posts Management
  static const posts = '/posts';
  static const postCreate = '/posts/create';
  static const postEdit = '/posts/edit';
  static const postView = '/posts/view';
  
  // Events Management
  static const events = '/events';
  static const eventCreate = '/events/create';
  static const eventEdit = '/events/edit';
  static const eventView = '/events/view';
  
  // Calendar Management
  static const calendar = '/calendar';
  static const calendarCreate = '/calendar/create';
  static const calendarEdit = '/calendar/edit';
  
  // Formation Management
  static const formation = '/formation';
  static const formationType = '/formation/types';
  static const formationStudy = '/formation/studies';
  static const formationQuiz = '/formation/quizzes';
  
  // Finance Management
  static const finance = '/finance';
  static const financeCreate = '/finance/create';
  static const financeEdit = '/finance/edit';
  
  // Profile
  static const profile = '/profile';
  static const settings = '/settings';
}
