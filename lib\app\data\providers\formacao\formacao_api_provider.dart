import 'package:get/get.dart';
import 'tipo_formacao_api_provider.dart';
import 'estudo_api_provider.dart';
import 'quiz_api_provider.dart';
import 'tema_api_provider.dart';
import 'enums_api_provider.dart';

/// Provider agregado para todas as operações de formação
class FormacaoApiProvider {
  late final TipoFormacaoApiProvider _tipoFormacaoProvider;
  late final EstudoApiProvider _estudoProvider;
  late final QuizApiProvider _quizProvider;
  late final TemaApiProvider _temaProvider;
  late final EnumsApiProvider _enumsProvider;

  FormacaoApiProvider() {
    _tipoFormacaoProvider = Get.put(TipoFormacaoApiProvider());
    _estudoProvider = Get.put(EstudoApiProvider());
    _quizProvider = Get.put(QuizApiProvider());
    _temaProvider = Get.put(TemaApiProvider());
    _enumsProvider = Get.put(EnumsApiProvider());
  }

  // ================================================================
  // GETTERS PARA ACESSO DIRETO AOS PROVIDERS
  // ================================================================

  /// Provider para operações de tipos de formação
  TipoFormacaoApiProvider get tipoFormacao => _tipoFormacaoProvider;

  /// Provider para operações de estudos
  EstudoApiProvider get estudo => _estudoProvider;

  /// Provider para operações de quizzes
  QuizApiProvider get quiz => _quizProvider;

  /// Provider para operações de temas
  TemaApiProvider get tema => _temaProvider;

  /// Provider para operações de enums (categorias e níveis)
  EnumsApiProvider get enums => _enumsProvider;
}
