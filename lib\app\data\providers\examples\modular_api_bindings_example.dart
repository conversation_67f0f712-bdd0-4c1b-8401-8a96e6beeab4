import 'package:get/get.dart';
import 'package:ibcb_desktop/app/data/providers/user/user_api_provider.dart';
import 'package:ibcb_desktop/app/data/providers/forum/forum_api_provider.dart';
import 'package:ibcb_desktop/app/data/providers/post/post_api_provider.dart';
import 'package:ibcb_desktop/app/data/providers/calendar/calendar_api_provider.dart';
import 'package:ibcb_desktop/app/data/providers/event/event_api_provider.dart';

/// Exemplo de binding usando a nova arquitetura modular
/// Mostra como injetar os providers específicos ao invés da classe Api monolítica
class ModularApiBindingsExample extends Bindings {
  @override
  void dependencies() {
    // Registrar os providers modulares
    Get.lazyPut<UserApiProvider>(() => UserApiProvider());
    Get.lazyPut<ForumApiProvider>(() => ForumApiProvider());
    Get.lazyPut<PostApiProvider>(() => PostApiProvider());
    Get.lazyPut<CalendarApiProvider>(() => CalendarApiProvider());
    Get.lazyPut<EventApiProvider>(() => EventApiProvider());

    // Exemplos de como injetar repositórios usando providers específicos
    // Get.lazyPut<AuthRepository>(() => AuthRepository(Get.find<UserApiProvider>()));
    // Get.lazyPut<ForumRepository>(() => ForumRepository(Get.find<ForumApiProvider>()));
    // Get.lazyPut<PostRepository>(() => PostRepository(Get.find<PostApiProvider>()));

    // Ou se preferir manter compatibilidade, a classe Api ainda funciona:
    // Get.lazyPut<Api>(() => Api());
  }
}
