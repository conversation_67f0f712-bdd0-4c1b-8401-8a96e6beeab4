import 'dart:io';
import 'package:flutter/foundation.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class Update<PERSON>hecker extends StatefulWidget {
  final Widget child;

  const UpdateChecker({super.key, required this.child});

  @override
  State<UpdateChecker> createState() => _UpdateCheckerState();
}

class _UpdateCheckerState extends State<UpdateChecker> {
  @override
  void initState() {
    super.initState();
    if (!kIsWeb && Platform.isAndroid) {
      _checkForUpdateAndroid();
    } else if (!kIsWeb && Platform.isWindows) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // _checkForUpdateWindows(context);
      });
    }
  }

  Future<void> _checkForUpdateAndroid() async {
    try {
      debugPrint('[UpdateChecker] Verificando atualizações para Android...');
      final AppUpdateInfo updateInfo = await InAppUpdate.checkForUpdate();

      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        debugPrint('[UpdateChecker] Nova atualização disponível');

        if (updateInfo.immediateUpdateAllowed) {
          debugPrint('[UpdateChecker] Iniciando atualização imediata');
          await InAppUpdate.performImmediateUpdate();
        } else if (updateInfo.flexibleUpdateAllowed) {
          debugPrint('[UpdateChecker] Iniciando atualização flexível');
          await InAppUpdate.startFlexibleUpdate();
        }
      }
    } catch (e, stack) {
      debugPrint('[UpdateChecker] Erro ao verificar atualizações: $e');
      debugPrint('[UpdateChecker] Stack trace: $stack');
    }
  }

  // Future<void> _checkForUpdateWindows(BuildContext context) async {
  //   if (!mounted) return;

  //   try {
  //     debugPrint('[UpdateChecker] Verificando atualizações para Windows...');
  //     final firestore = FirebaseFirestore.instance;
  //     final packageInfo = await PackageInfo.fromPlatform();
  //     final currentVersion = packageInfo.version;

  //     final doc =
  //         await firestore.collection('app_versions').doc('windows').get();

  //     if (!doc.exists || !mounted) {
  //       debugPrint(
  //         '[UpdateChecker] Documento não encontrado ou widget desmontado',
  //       );
  //       return;
  //     }

  //     final Map<String, dynamic> data = doc.data() ?? {};
  //     final String? latestVersion = data['latestVersion'] as String?;
  //     final String? downloadUrl = data['downloadUrl'] as String?;

  //     if (latestVersion == null || downloadUrl == null) {
  //       debugPrint('[UpdateChecker] Dados de versão incompletos');
  //       return;
  //     }

  //     if (_shouldUpdate(currentVersion, latestVersion)) {
  //       debugPrint(
  //         '[UpdateChecker] Update necessário: $currentVersion -> $latestVersion',
  //       );
  //       _showUpdateDialog(
  //         context,
  //         currentVersion,
  //         latestVersion,
  //         downloadUrl,
  //         data['releaseNotes'] as String? ?? '',
  //       );
  //     }
  //   } catch (e, stack) {
  //     debugPrint('[UpdateChecker] Erro ao verificar atualizações: $e');
  //     debugPrint('[UpdateChecker] Stack trace: $stack');
  //   }
  // }

  bool _shouldUpdate(String currentVersion, String latestVersion) {
    List<int> current = currentVersion.split('.').map(int.parse).toList();
    List<int> latest = latestVersion.split('.').map(int.parse).toList();

    for (int i = 0; i < current.length; i++) {
      if (latest[i] > current[i]) return true;
      if (latest[i] < current[i]) return false;
    }
    return false;
  }

  void _showUpdateDialog(
    BuildContext context,
    String currentVersion,
    String latestVersion,
    String downloadUrl,
    String releaseNotes,
  ) {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (_) => AlertDialog(
            title: const Text('Atualização Disponível!'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Uma nova versão do aplicativo está disponível.'),
                const SizedBox(height: 10),
                Text('Versão atual: $currentVersion'),
                Text('Nova versão: $latestVersion'),
                if (releaseNotes.isNotEmpty) ...[
                  const SizedBox(height: 10),
                  const Text('Notas da versão:'),
                  Text(releaseNotes),
                ],
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Depois'),
              ),
              TextButton(
                onPressed: () => launchUrl(Uri.parse(downloadUrl)),
                child: const Text('Atualizar Agora'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
