import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:ibcb_desktop/app/utils/windows_configs.dart';
// import 'package:firebase_core/firebase_core.dart';

import 'app/core/theme/app_theme.dart';
import 'app/routes/pages.dart';
import 'app/core/bindings/initial_binding.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  WindowsConfigs.setConfigs();

  // Initialize GetStorage
  await GetStorage.init();

  // Initialize Firebase
  // await Firebase.initializeApp();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'IBCB Desktop',
      debugShowCheckedModeBanner: false,

      // Theme Configuration
      theme: themeLight,
      darkTheme: themeDark,
      themeMode: ThemeMode.system,

      // Routing Configuration
      initialRoute: Routes.splashScreen,
      getPages: AppPages.pages,

      // Global Bindings
      initialBinding: InitialBinding(),

      // Error Handling
      unknownRoute: GetPage(
        name: '/not-found',
        page:
            () => const Scaffold(
              body: Center(child: Text('Página não encontrada')),
            ),
      ),
    );
  }
}
