class QuizModel {
  final int id;
  final int estudoId;
  final String pergunta;
  final int ordem;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<QuizAlternativaModel>? alternativas;

  QuizModel({
    required this.id,
    required this.estudoId,
    required this.pergunta,
    required this.ordem,
    this.createdAt,
    this.updatedAt,
    this.alternativas,
  });

  factory QuizModel.fromJson(Map<String, dynamic> json) {
    return QuizModel(
      id: json['id'] ?? 0,
      estudoId: json['estudo_id'] ?? 0,
      pergunta: json['pergunta'] ?? '',
      ordem: json['ordem'] ?? 1,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
      alternativas:
          json['alternativas'] != null
              ? (json['alternativas'] as List)
                  .map((e) => QuizAlternativaModel.fromJson(e))
                  .toList()
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'estudo_id': estudoId,
      'pergunta': pergunta,
      'ordem': ordem,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

class QuizAlternativaModel {
  final int id;
  final int quizId;
  final String letra;
  final String texto;
  final bool correta;
  final int ordem;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  QuizAlternativaModel({
    required this.id,
    required this.quizId,
    required this.letra,
    required this.texto,
    required this.correta,
    required this.ordem,
    this.createdAt,
    this.updatedAt,
  });

  factory QuizAlternativaModel.fromJson(Map<String, dynamic> json) {
    return QuizAlternativaModel(
      id: json['id'] ?? 0,
      quizId: json['quiz_id'] ?? 0,
      letra: json['letra'] ?? '',
      texto: json['texto'] ?? '',
      correta: _parseBool(json['correta']),
      ordem: json['ordem'] ?? 1,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }

  // Função auxiliar para converter int/bool
  static bool _parseBool(dynamic value) {
    if (value is bool) return value;
    if (value is int) return value == 1;
    if (value is String) return value == '1' || value.toLowerCase() == 'true';
    return false;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quiz_id': quizId,
      'letra': letra,
      'texto': texto,
      'correta': correta ? 1 : 0, // Converter bool para int
      'ordem': ordem,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

class RespostaQuizUsuarioModel {
  final int id;
  final int quizId;
  final int usuarioId;
  final String respostaUsuario;
  final bool correta;
  final DateTime? dataResposta;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  RespostaQuizUsuarioModel({
    required this.id,
    required this.quizId,
    required this.usuarioId,
    required this.respostaUsuario,
    required this.correta,
    this.dataResposta,
    this.createdAt,
    this.updatedAt,
  });

  factory RespostaQuizUsuarioModel.fromJson(Map<String, dynamic> json) {
    return RespostaQuizUsuarioModel(
      id: json['id'] ?? 0,
      quizId: json['quiz_id'] ?? 0,
      usuarioId: json['usuario_id'] ?? 0,
      respostaUsuario: json['resposta_usuario'] ?? '',
      correta: QuizAlternativaModel._parseBool(json['correta']),
      dataResposta:
          json['data_resposta'] != null
              ? DateTime.parse(json['data_resposta'])
              : null,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quiz_id': quizId,
      'usuario_id': usuarioId,
      'resposta_usuario': respostaUsuario,
      'correta': correta ? 1 : 0, // Converter bool para int
      'data_resposta': dataResposta?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

class ProgressoUsuarioModel {
  final int id;
  final int usuarioId;
  final int estudoId;
  final bool aprovado;
  final double? notaFinal;
  final DateTime? dataConclusao;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ProgressoUsuarioModel({
    required this.id,
    required this.usuarioId,
    required this.estudoId,
    required this.aprovado,
    this.notaFinal,
    this.dataConclusao,
    this.createdAt,
    this.updatedAt,
  });

  factory ProgressoUsuarioModel.fromJson(Map<String, dynamic> json) {
    return ProgressoUsuarioModel(
      id: json['id'] ?? 0,
      usuarioId: json['usuario_id'] ?? 0,
      estudoId: json['estudo_id'] ?? 0,
      aprovado: QuizAlternativaModel._parseBool(json['aprovado']),
      notaFinal: json['nota_final']?.toDouble(),
      dataConclusao:
          json['data_conclusao'] != null
              ? DateTime.parse(json['data_conclusao'])
              : null,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'usuario_id': usuarioId,
      'estudo_id': estudoId,
      'aprovado': aprovado ? 1 : 0, // Converter bool para int
      'nota_final': notaFinal,
      'data_conclusao': dataConclusao?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}
