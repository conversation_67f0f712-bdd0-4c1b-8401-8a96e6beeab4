class Role {
  int? id;
  String? name_role;
  Role({this.id, this.name_role});

  Map<String, dynamic> toMap() {
    return {'id': id, 'nome_cargo': name_role};
  }

  factory Role.fromMap(Map<String, dynamic> map) {
    print('Role.fromMap recebeu: $map'); // Debug log
    try {
      final role = Role(
        id:
            map['id'] is int
                ? map['id']
                : int.tryParse(map['id']?.toString() ?? '0'),
        name_role: map['nome_cargo']?.toString(),
      );
      print(
        'Role criado: id=${role.id}, name_role=${role.name_role}',
      ); // Debug log
      return role;
    } catch (e) {
      print('Erro ao criar Role: $e'); // Debug log
      rethrow;
    }
  }
}
