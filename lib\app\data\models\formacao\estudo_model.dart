import 'quiz_model.dart';

class EstudoModel {
  final int id;
  final int temaId;
  final int categoriaId;
  final int nivelId;
  final String titulo;
  final String? subtitulo;
  final String? texto;
  final String? links;
  final double notaMedia;
  final double aprovadoPorcentagem;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<ImagemEstudoModel>? imagens;
  final List<VideoEstudoModel>? videos;
  final List<DocumentoEstudoModel>? documentos;
  final List<QuizModel>? quizzes;

  EstudoModel({
    required this.id,
    required this.temaId,
    required this.categoriaId,
    required this.nivelId,
    required this.titulo,
    this.subtitulo,
    this.texto,
    this.links,
    required this.notaMedia,
    required this.aprovadoPorcentagem,
    this.createdAt,
    this.updatedAt,
    this.imagens,
    this.videos,
    this.documentos,
    this.quizzes,
  });

  factory EstudoModel.fromJson(Map<String, dynamic> json) {
    return EstudoModel(
      id: json['id'] ?? 0,
      temaId: json['tema_id'] ?? json['temaId'] ?? 0,
      categoriaId: json['categoria_id'] ?? json['categoriaId'] ?? 0,
      nivelId: json['nivel_id'] ?? json['nivelId'] ?? 0,
      titulo: json['titulo'] ?? '',
      subtitulo: json['subtitulo'],
      texto: json['texto'],
      links: json['links'],
      notaMedia: (json['nota_media'] ?? 0).toDouble(),
      aprovadoPorcentagem: (json['aprovado_porcentagem'] ?? 0).toDouble(),
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
      imagens:
          json['imagens'] != null
              ? (json['imagens'] as List)
                  .map((e) => ImagemEstudoModel.fromJson(e))
                  .toList()
              : null,
      videos:
          json['videos'] != null
              ? (json['videos'] as List)
                  .map((e) => VideoEstudoModel.fromJson(e))
                  .toList()
              : null,
      documentos:
          json['documentos'] != null
              ? (json['documentos'] as List)
                  .map((e) => DocumentoEstudoModel.fromJson(e))
                  .toList()
              : null,
      quizzes:
          json['quizzes'] != null
              ? (json['quizzes'] as List)
                  .map((e) => QuizModel.fromJson(e))
                  .toList()
              : null,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tema_id': temaId,
      'categoria_id': categoriaId,
      'nivel_id': nivelId,
      'titulo': titulo,
      'subtitulo': subtitulo,
      'texto': texto,
      'links': links,
      'nota_media': notaMedia,
      'aprovado_porcentagem': aprovadoPorcentagem,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

class ImagemEstudoModel {
  final int id;
  final int estudoId;
  final String url;
  final int ordem;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  ImagemEstudoModel({
    required this.id,
    required this.estudoId,
    required this.url,
    required this.ordem,
    this.createdAt,
    this.updatedAt,
  });

  factory ImagemEstudoModel.fromJson(Map<String, dynamic> json) {
    return ImagemEstudoModel(
      id: json['id'] ?? 0,
      estudoId: json['estudo_id'] ?? 0,
      url: json['url'] ?? '',
      ordem: json['ordem'] ?? 1,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'estudo_id': estudoId,
      'url': url,
      'ordem': ordem,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

class VideoEstudoModel {
  final int id;
  final int estudoId;
  final String url;
  final int ordem;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  VideoEstudoModel({
    required this.id,
    required this.estudoId,
    required this.url,
    required this.ordem,
    this.createdAt,
    this.updatedAt,
  });

  factory VideoEstudoModel.fromJson(Map<String, dynamic> json) {
    return VideoEstudoModel(
      id: json['id'] ?? 0,
      estudoId: json['estudo_id'] ?? 0,
      url: json['url'] ?? '',
      ordem: json['ordem'] ?? 1,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'estudo_id': estudoId,
      'url': url,
      'ordem': ordem,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

class DocumentoEstudoModel {
  final int id;
  final int estudoId;
  final String url;
  final int ordem;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  DocumentoEstudoModel({
    required this.id,
    required this.estudoId,
    required this.url,
    required this.ordem,
    this.createdAt,
    this.updatedAt,
  });

  factory DocumentoEstudoModel.fromJson(Map<String, dynamic> json) {
    return DocumentoEstudoModel(
      id: json['id'] ?? 0,
      estudoId: json['estudo_id'] ?? 0,
      url: json['url'] ?? '',
      ordem: json['ordem'] ?? 1,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'estudo_id': estudoId,
      'url': url,
      'ordem': ordem,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}
