import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum FinanceType { caixa, cantina }

class FinanceTypeService {
  static Future<FinanceType?> showFinanceTypeDialog() async {
    return await Get.dialog<FinanceType>(
      AlertDialog(
        title: const Text('Selecione o Tipo'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Caixa'),
              onTap: () => Get.back(result: FinanceType.caixa),
            ),
            ListTile(
              title: const Text('Cantina e Outros'),
              onTap: () => Get.back(result: FinanceType.cantina),
            ),
          ],
        ),
      ),
    );
  }
}
