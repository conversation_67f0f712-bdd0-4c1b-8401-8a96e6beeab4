import 'package:flutter/material.dart';
import 'package:ibcb_desktop/app/data/models/user_login_req_model.dart';
import 'package:ibcb_desktop/app/data/models/user_login_res_model.dart';
import 'package:ibcb_desktop/app/data/models/user_model.dart';
import 'package:ibcb_desktop/app/data/models/user_profile_model.dart';
import 'package:ibcb_desktop/app/data/providers/api.dart';

class AuthRepository {
  final Api _api;

  AuthRepository(this._api);

  Future<UserLoginResponseModel> login(
    UserLoginReqModel userLoginReqModel,
  ) async => _api.login(userLoginReqModel);

  Future<void> register(UserLoginReqModel userLoginReqModel) async =>
      _api.register(userLoginReqModel);

  Future<void> updateProfile(UserProfileModel user) async {
    debugPrint("AuthRepository: update<PERSON>rofile called with ${user.toMap()}");
    await _api.updateProfile(user);
  }

  Future<Map<String, dynamic>> authenticateWithGoogle(String idToken) async {
    return _api.authenticateWithGoogle(idToken);
  }

  Future<void> logout() async {
    await _api.logout();
  }

  Future<void> sendFcmTokenToServer(String fcmToken, String accessToken) async {
    await _api.sendFcmTokenToServer(fcmToken, accessToken);
  }

  Future<UserModel> getUser() async {
    return _api.getUser();
  }
}
