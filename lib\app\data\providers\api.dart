import 'package:get/instance_manager.dart';

import 'package:ibcb_desktop/app/data/providers/calendar/calendar_api_provider.dart';
import 'package:ibcb_desktop/app/data/providers/event/event_api_provider.dart';
import 'package:ibcb_desktop/app/data/providers/formacao/formacao_api_provider.dart';
import 'package:ibcb_desktop/app/data/providers/forum/forum_api_provider.dart';
import 'package:ibcb_desktop/app/data/providers/post/post_api_provider.dart';
import 'package:ibcb_desktop/app/data/providers/user/user_api_provider.dart';

/// API principal que agrega todos os providers modulares
/// Mantém compatibilidade com o código existente enquanto usa a nova arquitetura modular
class Api {
  // Instâncias dos providers modulares
  late final UserApiProvider _userProvider;
  late final ForumApiProvider _forumProvider;
  late final PostApiProvider _postProvider;
  late final EventApiProvider _eventProvider;
  late final CalendarApiProvider _calendarProvider;
  late final FormacaoApiProvider _formacaoProvider;

  Api() {
    _userProvider = Get.put(UserApiProvider());
    _forumProvider = Get.put(ForumApiProvider());
    _postProvider = Get.put(PostApiProvider());
    _eventProvider = Get.put(EventApiProvider());
    _calendarProvider = Get.put(CalendarApiProvider());
    _formacaoProvider = Get.put(FormacaoApiProvider());
  }

  // ================================================================
  // GETTERS PARA ACESSO DIRETO AOS PROVIDERS
  // ================================================================

  /// Provider para operações de usuários
  UserApiProvider get user => _userProvider;

  /// Provider para operações do fórum
  ForumApiProvider get forum => _forumProvider;

  /// Provider para operações de posts
  PostApiProvider get post => _postProvider;

  /// Provider para operações de eventos simples
  EventApiProvider get event => _eventProvider;

  /// Provider para operações do calendário
  CalendarApiProvider get calendar => _calendarProvider;

  /// Provider para operações de formação
  Future<dynamic> get formacao async => _formacaoProvider;

  // ================================================================
  // MÉTODOS DE COMPATIBILIDADE (delegando para os providers)
  // ================================================================

  // User methods
  getUser() => _userProvider.getUser();
  login(data) => _userProvider.login(data);
  authenticateWithGoogle(idToken) =>
      _userProvider.authenticateWithGoogle(idToken);
  sendFcmTokenToServer(fcmToken, accessToken) =>
      _userProvider.sendFcmTokenToServer(fcmToken, accessToken);
  register(data) => _userProvider.register(data);
  logout() => _userProvider.logout();
  notifyApiPasswordChanged(email, newPassword, firebaseToken) =>
      _userProvider.notifyApiPasswordChanged(email, newPassword, firebaseToken);
  fetchUsers() => _userProvider.fetchUsers();
  updateProfile(data) => _userProvider.updateProfile(data);
  fetchCargos() => _userProvider.fetchCargos();
  updateUserCargos(userId, cargoIds) =>
      _userProvider.updateUserCargos(userId, cargoIds);

  // Forum methods
  fetchForumTopics({page = 1, perPage = 5}) =>
      _forumProvider.fetchForumTopics(page: page, perPage: perPage);
  createForumTopic(data) => _forumProvider.createForumTopic(data);
  updateForumTopic(id, data) => _forumProvider.updateForumTopic(id, data);
  deleteForumTopic(id) => _forumProvider.deleteForumTopic(id);
  fetchForumTopicById(topicId) => _forumProvider.fetchForumTopicById(topicId);
  changeForumStatus(topicId, fechado) =>
      _forumProvider.changeForumStatus(topicId, fechado);
  getForumComments(topicId) => _forumProvider.getForumComments(topicId);
  getForumCommentsTree(topicId) => _forumProvider.getForumCommentsTree(topicId);
  createForumComment(topicId, content, {parentId}) =>
      _forumProvider.createForumComment(topicId, content, parentId: parentId);
  replyToForumComment(commentId, content) =>
      _forumProvider.replyToForumComment(commentId, content);
  deleteForumComment(commentId) => _forumProvider.deleteForumComment(commentId);

  // Post methods
  getPosts({page = 1, perPage = 5}) =>
      _postProvider.getPosts(page: page, perPage: perPage);
  createPost(data) => _postProvider.createPost(data);
  updatePost(id, data) => _postProvider.updatePost(id, data);
  deletePost(id) => _postProvider.deletePost(id);
  createCommentPost(forumId, content) =>
      _postProvider.createCommentPost(forumId, content);
  deleteCommentsPost(id) => _postProvider.deleteCommentsPost(id);

  // Event methods
  getEvent() => _eventProvider.getEvent();
  createEvent(data) => _eventProvider.createEvent(data);
  deleteEvent(eventId) => _eventProvider.deleteEvent(eventId);

  // Calendar methods
  getCalendarEvents({page = 1, perPage = 1000}) =>
      _calendarProvider.getCalendarEvents(page: page, perPage: perPage);
  createCalendarEvent(data) => _calendarProvider.createCalendarEvent(data);
  getCalendarEventById(eventId) =>
      _calendarProvider.getCalendarEventById(eventId);
  updateCalendarEvent(eventId, data) =>
      _calendarProvider.updateCalendarEvent(eventId, data);
  deleteCalendarEvent(eventId) =>
      _calendarProvider.deleteCalendarEvent(eventId);
  participateInEvent(eventId) => _calendarProvider.participateInEvent(eventId);
  leaveEvent(eventId) => _calendarProvider.leaveEvent(eventId);
  getCalendarCategories() => _calendarProvider.getCalendarCategories();
  getCalendarEventsByDate(date) =>
      _calendarProvider.getCalendarEventsByDate(date);
  getUpcomingCalendarEvents({limit = 5}) =>
      _calendarProvider.getUpcomingCalendarEvents(limit: limit);
}
