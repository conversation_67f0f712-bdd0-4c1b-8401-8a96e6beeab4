import 'package:dio/dio.dart';
import '../base/base_api_provider.dart';
import '../../models/formacao/categoria_enum_model.dart';
import '../../models/formacao/nivel_enum_model.dart';

class EnumsApiProvider extends BaseApiProvider {
  Dio get dio => super.dio;

  Future<List<CategoriaEnumModel>> getCategorias() async {
    try {
      final response = await dio.get('/categorias');
      if (response.data != null) {
        return (response.data as List)
            .map((item) => CategoriaEnumModel.fromJson(item))
            .toList();
      }
      return [];
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<List<NivelEnumModel>> getNiveis() async {
    try {
      final response = await dio.get('/niveis');
      if (response.data != null) {
        return (response.data as List)
            .map((item) => NivelEnumModel.fromJson(item))
            .toList();
      }
      return [];
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<CategoriaEnumModel> createCategoria(Map<String, dynamic> data) async {
    try {
      final response = await dio.post('/categorias', data: data);
      return CategoriaEnumModel.fromJson(response.data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }

  Future<NivelEnumModel> createNivel(Map<String, dynamic> data) async {
    try {
      final response = await dio.post('/niveis', data: data);
      return NivelEnumModel.fromJson(response.data);
    } on DioException catch (e) {
      throw handleDioException(e);
    }
  }
}
