import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ibcb_desktop/app/core/theme/app_theme.dart';

class ThemeSwitch extends StatelessWidget {
  const ThemeSwitch({super.key});

  @override
  Widget build(BuildContext context) {
    final themeService = Get.find<ThemeService>();

    return Obx(() {
      return IconButton(
        icon: Icon(
          themeService.isDarkMode ? Icons.dark_mode : Icons.light_mode,
          color: Colors.blue,
          size: 27,
        ),
        onPressed: () => themeService.changeTheme(),
      );
    });
  }
}
