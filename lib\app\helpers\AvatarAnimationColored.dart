// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

class AvatarAnimationColored extends StatelessWidget {
  const AvatarAnimationColored({
    Key? key,
    required this.radius,
  }) : super(key: key);
  final double radius;
  @override
  Widget build(BuildContext context) {
    double end = 360 * 10000;
    int durantion = 4 * 10000;

    return TweenAnimationBuilder(
      tween: Tween<double>(
        begin: 0,
        end: end,
      ),
      duration: Duration(seconds: durantion),
      builder: (BuildContext context, double angle, Widget? child) {
        return Transform.rotate(
          angle: angle * (3.14159265359 / 180),
          child: Container(
            width: radius * 2,
            height: radius * 2,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  Colors.red,
                  Colors.orange,
                  Colors.yellow,
                  Colors.green,
                  Colors.blue,
                  Colors.purple.shade300,
                  Colors.indigo,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
        );
      },
    );
  }
}
