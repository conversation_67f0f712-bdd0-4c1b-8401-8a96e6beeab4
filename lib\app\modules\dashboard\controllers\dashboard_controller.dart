import 'package:get/get.dart';
import '../../../data/services/auth/auth_service.dart';
import '../../../data/models/user_model.dart';
import '../../../core/logger/app_logger.dart';
import '../../../routes/pages.dart';

class DashboardController extends GetxController {
  static final _logger = AppLogger.logger;

  final AuthService _authService = Get.find<AuthService>();

  final selectedIndex = 0.obs;
  final isDrawerOpen = false.obs;

  // Dashboard stats
  final totalUsers = 0.obs;
  final totalPosts = 0.obs;
  final totalEvents = 0.obs;
  final totalForumTopics = 0.obs;

  final isLoading = false.obs;

  UserModel? get currentUser => _authService.user.value;

  final List<MenuItem> menuItems = [
    MenuItem(
      title: 'Dashboard',
      icon: 'dashboard',
      route: Routes.dashboard,
      roles: [],
    ),
    MenuItem(
      title: 'Usuários',
      icon: 'people',
      route: Routes.users,
      roles: [1], // Admin role
    ),
    MenuItem(
      title: 'Posts',
      icon: 'article',
      route: Routes.posts,
      roles: [1, 2], // Admin and moderator
    ),
    MenuItem(title: 'Fórum', icon: 'forum', route: Routes.forum, roles: [1, 2]),
    MenuItem(
      title: 'Eventos',
      icon: 'event',
      route: Routes.events,
      roles: [1, 2],
    ),
    MenuItem(
      title: 'Calendário',
      icon: 'calendar_today',
      route: Routes.calendar,
      roles: [1, 2],
    ),
    MenuItem(
      title: 'Formação',
      icon: 'school',
      route: Routes.formation,
      roles: [1, 2],
    ),
    MenuItem(
      title: 'Finanças',
      icon: 'attach_money',
      route: Routes.finance,
      roles: [1], // Admin only
    ),
  ];

  @override
  void onInit() {
    super.onInit();
    _logger.debug('DashboardController initialized');
    loadDashboardData();
  }

  @override
  void onReady() {
    super.onReady();
    // Refresh data when dashboard is ready
    refreshDashboardData();
  }

  void selectMenuItem(int index) {
    selectedIndex.value = index;
    final menuItem = menuItems[index];

    // Check user permissions
    if (canAccessRoute(menuItem)) {
      Get.toNamed(menuItem.route);
    } else {
      Get.snackbar(
        'Acesso Negado',
        'Você não tem permissão para acessar esta seção',
        snackPosition: SnackPosition.TOP,
      );
    }
  }

  bool canAccessRoute(MenuItem menuItem) {
    if (currentUser == null) return false;
    if (menuItem.roles.isEmpty) return true; // Public route

    return currentUser!.cargos?.any(
          (role) => menuItem.roles.contains(role.id),
        ) ??
        false;
  }

  void toggleDrawer() {
    isDrawerOpen.value = !isDrawerOpen.value;
  }

  Future<void> loadDashboardData() async {
    try {
      isLoading(true);

      // Load dashboard statistics
      await Future.wait([
        loadUserStats(),
        loadPostStats(),
        loadEventStats(),
        loadForumStats(),
      ]);

      _logger.info('Dashboard data loaded successfully');
    } catch (e) {
      _logger.error('Failed to load dashboard data', e);
      Get.snackbar(
        'Erro',
        'Erro ao carregar dados do dashboard: ${e.toString()}',
      );
    } finally {
      isLoading(false);
    }
  }

  Future<void> loadUserStats() async {
    try {
      // Simulate API call - replace with actual implementation
      await Future.delayed(const Duration(milliseconds: 500));
      totalUsers.value = 250; // This would come from API
    } catch (e) {
      _logger.error('Failed to load user stats', e);
    }
  }

  Future<void> loadPostStats() async {
    try {
      // Simulate API call - replace with actual implementation
      await Future.delayed(const Duration(milliseconds: 500));
      totalPosts.value = 1250; // This would come from API
    } catch (e) {
      _logger.error('Failed to load post stats', e);
    }
  }

  Future<void> loadEventStats() async {
    try {
      // Simulate API call - replace with actual implementation
      await Future.delayed(const Duration(milliseconds: 500));
      totalEvents.value = 45; // This would come from API
    } catch (e) {
      _logger.error('Failed to load event stats', e);
    }
  }

  Future<void> loadForumStats() async {
    try {
      // Simulate API call - replace with actual implementation
      await Future.delayed(const Duration(milliseconds: 500));
      totalForumTopics.value = 89; // This would come from API
    } catch (e) {
      _logger.error('Failed to load forum stats', e);
    }
  }

  Future<void> refreshDashboardData() async {
    await loadDashboardData();
  }

  Future<void> logout() async {
    try {
      await _authService.logout();
      Get.offAllNamed(Routes.login);
    } catch (e) {
      _logger.error('Logout failed', e);
      Get.snackbar('Erro', 'Falha ao fazer logout');
    }
  }

  void goToProfile() {
    Get.toNamed(Routes.profile);
  }

  void goToSettings() {
    Get.toNamed(Routes.settings);
  }
}

class MenuItem {
  final String title;
  final String icon;
  final String route;
  final List<int> roles; // Role IDs that can access this menu item

  MenuItem({
    required this.title,
    required this.icon,
    required this.route,
    required this.roles,
  });
}
