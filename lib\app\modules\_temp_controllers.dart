import 'package:get/get.dart';

// Forum
class ForumController extends GetxController {}
class ForumBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<ForumController>(() => ForumController());
  }
}

// Events
class EventsController extends GetxController {}
class EventsBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<EventsController>(() => EventsController());
  }
}

// Calendar
class CalendarController extends GetxController {}
class CalendarBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<CalendarController>(() => CalendarController());
  }
}

// Formation
class FormationController extends GetxController {}
class FormationBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<FormationController>(() => FormationController());
  }
}

// Finance
class FinanceController extends GetxController {}
class FinanceBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<FinanceController>(() => FinanceController());
  }
}
