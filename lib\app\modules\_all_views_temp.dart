import 'package:flutter/material.dart';

// Events Views
class EventsView extends StatelessWidget {
  const EventsView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Eventos'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Gerenciamento de Eventos em desenvolvimento...')),
  );
}

class EventCreateView extends StatelessWidget {
  const EventCreateView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Criar Evento'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Criar evento em desenvolvimento...')),
  );
}

class EventEditView extends StatelessWidget {
  const EventEditView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Editar Evento'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Editar evento em desenvolvimento...')),
  );
}

class EventDetailView extends StatelessWidget {
  const EventDetailView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Detalhes do Evento'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Detalhes do evento em desenvolvimento...')),
  );
}

// Calendar Views
class CalendarView extends StatelessWidget {
  const CalendarView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Calendário'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Calendário em desenvolvimento...')),
  );
}

class CalendarCreateView extends StatelessWidget {
  const CalendarCreateView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Criar Evento no Calendário'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Criar evento no calendário em desenvolvimento...')),
  );
}

class CalendarEditView extends StatelessWidget {
  const CalendarEditView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Editar Evento do Calendário'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Editar evento do calendário em desenvolvimento...')),
  );
}

// Formation Views
class FormationView extends StatelessWidget {
  const FormationView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Formação'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Gerenciamento de Formação em desenvolvimento...')),
  );
}

class FormationTypesView extends StatelessWidget {
  const FormationTypesView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Tipos de Formação'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Tipos de formação em desenvolvimento...')),
  );
}

class FormationStudiesView extends StatelessWidget {
  const FormationStudiesView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Estudos'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Estudos em desenvolvimento...')),
  );
}

class FormationQuizzesView extends StatelessWidget {
  const FormationQuizzesView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Quizzes'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Quizzes em desenvolvimento...')),
  );
}

// Finance Views
class FinanceView extends StatelessWidget {
  const FinanceView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Finanças'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Gerenciamento Financeiro em desenvolvimento...')),
  );
}

class FinanceCreateView extends StatelessWidget {
  const FinanceCreateView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Nova Transação'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Nova transação financeira em desenvolvimento...')),
  );
}

class FinanceEditView extends StatelessWidget {
  const FinanceEditView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('Editar Transação'), backgroundColor: Theme.of(context).colorScheme.surface),
    body: const Center(child: Text('Editar transação financeira em desenvolvimento...')),
  );
}
