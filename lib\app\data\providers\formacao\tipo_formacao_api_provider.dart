import 'package:ibcb_desktop/app/data/providers/base/base_api_provider.dart';
import '../../models/formacao/tipo_formacao_model.dart';

class TipoFormacaoApiProvider extends BaseApiProvider {
  Future<List<TipoFormacaoModel>> getTiposFormacao() async {
    return executeWithRetry(() async {
      final response = await dio.get('/tipos-formacao');
      final List<dynamic> data = response.data;
      return data.map((json) => TipoFormacaoModel.fromJson(json)).toList();
    });
  }

  Future<TipoFormacaoModel> getTipoFormacao(int id) async {
    return executeWithRetry(() async {
      final response = await dio.get('/tipos-formacao/$id');
      return TipoFormacaoModel.fromJson(response.data);
    });
  }

  Future<TipoFormacaoModel> createTipoFormacao(
    TipoFormacaoModel tipoFormacao,
  ) async {
    return executeWithRetry(() async {
      final response = await dio.post(
        '/tipos-formacao',
        data: tipoFormacao.toJson(),
      );
      return TipoFormacaoModel.fromJson(response.data);
    });
  }

  Future<TipoFormacaoModel> updateTipoFormacao(
    int id,
    TipoFormacaoModel tipoFormacao,
  ) async {
    return executeWithRetry(() async {
      final response = await dio.put(
        '/tipos-formacao/$id',
        data: tipoFormacao.toJson(),
      );
      return TipoFormacaoModel.fromJson(response.data);
    });
  }

  Future<void> deleteTipoFormacao(int id) async {
    return executeWithRetry(() async {
      await dio.delete('/tipos-formacao/$id');
    });
  }
}
